# Migration: AddOldFieldsToCompanies
# Purpose: Adds legacy tracking fields to companies table for data migration audit trail
#
# These fields store references to the original legacy database records to:
# - Enable data traceability between old and new systems
# - Support data validation and debugging during migration
# - Maintain audit trail of when and by whom records were last modified in legacy system
# - Allow for re-running migrations without creating duplicates
class AddOldFieldsToCompanies < ActiveRecord::Migration[7.0]
  def change
    # Legacy system tracking fields for audit trail and migration support
    add_column :companies, :old_CMPYID, :string        # Original company ID from legacy tbl_company table
    add_column :companies, :old_LASTMDFUSER, :string   # Last modification user in legacy system
    add_column :companies, :old_LASTMDFDATE, :datetime # Last modification date in legacy system
  end
end
