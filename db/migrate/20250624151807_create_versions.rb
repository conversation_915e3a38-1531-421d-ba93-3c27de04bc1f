# Migration: CreateVersions
# Purpose: Creates the versions table to track application version deployments
#
# This table maintains a record of:
# - Application version numbers
# - Deployment dates
# - Release notes and remarks for each version
#
# Useful for:
# - Tracking deployment history
# - Displaying current version information to users
# - Maintaining release notes and change logs
class CreateVersions < ActiveRecord::Migration[7.0]
  def change
    create_table :versions do |t|
      t.string :version                       # Version number (e.g., "1.0.0", "2.1.3")
      t.text :remarks                         # Release notes, change log, or deployment remarks
      t.date :deployed_at                     # Date when this version was deployed

      # Rails standard timestamp fields
      t.timestamps                            # created_at and updated_at timestamps
    end
  end
end
