# Migration: AddOldFieldsToProducts
# Purpose: Adds legacy tracking fields to products table for data migration audit trail
#
# These fields store references to the original legacy database records to:
# - Enable data traceability between old and new systems
# - Support data validation and debugging during migration
# - Maintain audit trail of when and by whom records were created/modified in legacy system
# - Allow for re-running migrations without creating duplicates
class AddOldFieldsToProducts < ActiveRecord::Migration[7.0]
  def change
    # Legacy system tracking fields for audit trail and migration support
    add_column :products, :old_PDID, :string           # Original product ID from legacy tbl_pd table
    add_column :products, :old_LASTMDFUSER, :string    # Last modification user in legacy system
    add_column :products, :old_LASTMDFDATE, :datetime  # Last modification date in legacy system
    add_column :products, :old_CREATEDATE, :datetime   # Creation date in legacy system
  end
end
