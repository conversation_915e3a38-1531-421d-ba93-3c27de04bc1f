# Migration: AddDetailColumnsToUsers
# Purpose: Adds detailed user profile information and permission fields to users table
#
# This migration extends the basic Devise user model with:
# - Personal information (names in multiple languages, title, gender)
# - Contact information (phone number)
# - Job-related information (job title)
# - Permission flags for different system roles
# - Additional remarks field
#
# Supports multilingual names with separate fields for:
# - Default language (Traditional Chinese)
# - Simplified Chinese (_cn suffix)
# - English (_en suffix)
class AddDetailColumnsToUsers < ActiveRecord::Migration[7.0]
  def change
    # User identification and login
    add_column :users, :username, :string              # Username for display purposes

    # Personal information
    add_column :users, :title, :string                 # Personal title (Mr., Ms., Dr., etc.)
    add_column :users, :first_name, :string            # First name (default: Traditional Chinese)
    add_column :users, :first_name_cn, :string         # First name in Simplified Chinese
    add_column :users, :first_name_en, :string         # First name in English
    add_column :users, :last_name, :string             # Last name (default: Traditional Chinese)
    add_column :users, :last_name_cn, :string          # Last name in Simplified Chinese
    add_column :users, :last_name_en, :string          # Last name in English
    add_column :users, :gender, :string                # Gender

    # Professional information
    add_column :users, :job_title, :string             # Job title/position

    # Contact information
    add_column :users, :tel_num, :string               # Telephone number

    # Permission flags for role-based access control
    add_column :users, :is_gtin_management, :boolean, default: false    # Can manage GTIN/barcode assignments
    add_column :users, :is_system_admin, :boolean, default: false       # System administrator privileges
    add_column :users, :is_user_management, :boolean, default: false    # Can manage other users

    # Additional information
    add_column :users, :remark, :text                  # Additional remarks or notes about the user
  end
end
