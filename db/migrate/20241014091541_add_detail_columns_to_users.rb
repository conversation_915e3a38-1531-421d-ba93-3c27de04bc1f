class AddDetailColumnsToUsers < ActiveRecord::Migration[7.0]
  def change
    add_column :users, :username, :string
    add_column :users, :title, :string
    add_column :users, :first_name, :string
    add_column :users, :first_name_cn, :string
    add_column :users, :first_name_en, :string
    add_column :users, :last_name, :string
    add_column :users, :last_name_cn, :string
    add_column :users, :last_name_en, :string
    add_column :users, :gender, :string
    add_column :users, :job_title, :string
    add_column :users, :tel_num, :string
    add_column :users, :is_gtin_management, :boolean, default: false
    add_column :users, :is_system_admin, :boolean, default: false
    add_column :users, :is_user_management, :boolean, default: false
    add_column :users, :remark, :text
  end
end
