class AddUsefulInformationToProducts < ActiveRecord::Migration[7.0]
  def change
    add_column :products, :use_function, :text
    add_column :products, :use_function_cn, :text
    add_column :products, :use_function_en, :text

    add_column :products, :direction_in_usage, :text
    add_column :products, :direction_in_usage_cn, :text
    add_column :products, :direction_in_usage_en, :text

    add_column :products, :caution, :text
    add_column :products, :caution_cn, :text
    add_column :products, :caution_en, :text

    add_column :products, :storage_instruction, :text
    add_column :products, :storage_instruction_cn, :text
    add_column :products, :storage_instruction_en, :text

    add_column :products, :benefit, :text
    add_column :products, :benefit_cn, :text
    add_column :products, :benefit_en, :text

    add_column :products, :remark, :text
    add_column :products, :remark_cn, :text
    add_column :products, :remark_en, :text

    add_column :products, :is_brand_owner, :boolean
    add_column :products, :is_manufacturer, :boolean

    add_column :products, :manufacturer_gln, :string
    add_column :products, :manufacturer_name, :string
    add_column :products, :manufacturer_address, :string
    add_column :products, :manufacturer_telephone, :string
    add_column :products, :manufacturer_fax, :string
    add_column :products, :manufacturer_email, :string
    add_column :products, :manufacturer_website, :string

    add_column :products, :is_distributor, :boolean
    add_column :products, :distributor_name, :string
    add_column :products, :distributor_address, :string
    add_column :products, :distributor_telephone, :string
    add_column :products, :distributor_fax, :string
    add_column :products, :distributor_email, :string
    add_column :products, :distributor_website, :string

    add_column :products, :is_retailer, :boolean

    # Packaging
    add_column :products, :outer_packaging_type, :string
    add_column :products, :number_of_layers_per_carton, :integer
    add_column :products, :number_of_trade_items_per_carton_layer, :integer
    add_column :products, :number_of_layers_per_pailet, :integer
    add_column :products, :number_of_trade_items_per_pailet_layer, :integer
    add_column :products, :total_number_of_trade_items_per_pailet, :integer

    # Price
    add_column :products, :price, :decimal, precision: 10, scale: 2
    add_column :products, :price_currency, :string
    add_column :products, :basic_unit, :decimal, precision: 10, scale: 2
    add_column :products, :basic_unit_unit, :string

    # Discount Price
    add_column :products, :discount_price, :decimal, precision: 10, scale: 2
    add_column :products, :discount_price_currency, :string
    add_column :products, :discount_basic_unit, :decimal, precision: 10, scale: 2
    add_column :products, :discount_basic_unit_unit, :string

  end
end
