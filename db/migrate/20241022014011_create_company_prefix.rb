# Migration: CreateCompanyPrefix
# Purpose: Creates the company_prefixes table to manage GTIN prefix assignments
#
# This table manages the assignment of GCP (Global Company Prefix) keys to companies.
# GCP keys are essential for barcode generation as they:
# - Uniquely identify the company in the global GS1 system
# - Form the first part of GTINs (Global Trade Item Numbers)
# - Ensure barcode uniqueness across the global supply chain
#
# Each company can have multiple prefixes of different types for various purposes.
class CreateCompanyPrefix < ActiveRecord::Migration[7.0]
  def change
    create_table :company_prefixes do |t|
      t.string :gcpkey                                  # Global Company Prefix key (the actual prefix number)
      t.string :gcptype                                 # Type/category of the GCP (e.g., standard, special purpose)
      t.references :company, null: false, foreign_key: true  # Foreign key to companies table

      # Rails standard timestamp fields
      t.timestamps                                      # created_at and updated_at timestamps
    end
  end
end
