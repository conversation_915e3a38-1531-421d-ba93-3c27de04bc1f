# Migration: CreateCompanies
# Purpose: Creates the companies table to store company information
#
# This table supports multilingual content with separate fields for:
# - Default language (Traditional Chinese)
# - Simplified Chinese (_cn suffix)
# - English (_en suffix)
#
# The table stores comprehensive company information including:
# - Basic identification (IPGLN, prefix)
# - Contact information (email, phone, fax, website)
# - Address information in multiple languages
# - Person in charge details
# - Contact person details
class CreateCompanies < ActiveRecord::Migration[7.0]
  def change
    create_table :companies do |t|
      # Company identification
      t.string :ipgln                          # International Product Group Location Number (unique identifier)
      t.string :prefix                         # Company prefix for barcode generation

      # Company name in multiple languages
      t.string :name                           # Company name (default: Traditional Chinese)
      t.string :name_cn                        # Company name in Simplified Chinese
      t.string :name_en                        # Company name in English

      # Contact information
      t.string :email                          # Primary company email address
      t.string :website_url                    # Company website URL
      t.string :telephone                      # Primary telephone number
      t.string :fax                            # Fax number

      # Address information in multiple languages
      # Default language (Traditional Chinese)
      t.string :location                       # Building/location name
      t.string :location_cn                    # Building/location name in Simplified Chinese
      t.string :location_en                    # Building/location name in English
      t.string :floor                          # Floor information
      t.string :floor_cn                       # Floor information in Simplified Chinese
      t.string :floor_en                       # Floor information in English
      t.string :street                         # Street address
      t.string :street_cn                      # Street address in Simplified Chinese
      t.string :street_en                      # Street address in English
      t.string :city                           # City name
      t.string :city_cn                        # City name in Simplified Chinese
      t.string :city_en                        # City name in English
      t.string :country                        # Country

      # Person in charge information in multiple languages
      t.string :in_charge_person_name          # Name of person in charge (default language)
      t.string :in_charge_person_name_cn       # Name of person in charge in Simplified Chinese
      t.string :in_charge_person_name_en       # Name of person in charge in English
      t.string :in_charge_job_title            # Job title of person in charge
      t.string :in_charge_email                # Email of person in charge
      t.string :in_charge_telephone            # Phone number of person in charge

      # Contact person information in multiple languages (may be different from person in charge)
      t.string :contact_person_name            # Contact person name (default language)
      t.string :contact_person_name_cn         # Contact person name in Simplified Chinese
      t.string :contact_person_name_en         # Contact person name in English
      t.string :contact_job_title             # Contact person job title
      t.string :contact_email                 # Contact person email address
      t.string :contact_telephone             # Contact person phone number

      # Rails standard timestamp fields
      t.timestamps                            # created_at and updated_at timestamps
    end
  end
end
