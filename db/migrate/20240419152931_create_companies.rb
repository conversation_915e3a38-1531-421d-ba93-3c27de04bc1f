class CreateCompanies < ActiveRecord::Migration[7.0]
  def change
    create_table :companies do |t|
      t.string :ipgln
      t.string :prefix
      t.string :name
      t.string :name_cn
      t.string :name_en
      t.string :email
      t.string :website_url
      t.string :telephone
      t.string :fax
      t.string :location
      t.string :location_cn
      t.string :location_en
      t.string :floor
      t.string :floor_cn
      t.string :floor_en
      t.string :street
      t.string :street_cn
      t.string :street_en
      t.string :city
      t.string :city_cn
      t.string :city_en
      t.string :country
      t.string :in_charge_person_name
      t.string :in_charge_person_name_cn
      t.string :in_charge_person_name_en
      t.string :in_charge_job_title
      t.string :in_charge_email
      t.string :in_charge_telephone
      t.string :contact_person_name
      t.string :contact_person_name_cn
      t.string :contact_person_name_en
      t.string :contact_job_title
      t.string :contact_email
      t.string :contact_telephone

      t.timestamps
    end
  end
end
