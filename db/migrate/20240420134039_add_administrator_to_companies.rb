class AddAdministratorToCompanies < ActiveRecord::Migration[7.0]
  def change
    add_column :companies, :admin_first_name, :string
    add_column :companies, :admin_first_name_cn, :string
    add_column :companies, :admin_first_name_en, :string

    add_column :companies, :admin_last_name, :string
    add_column :companies, :admin_last_name_cn, :string
    add_column :companies, :admin_last_name_en, :string

    add_column :companies, :admin_title, :string
    add_column :companies, :admin_gender, :string
    add_column :companies, :admin_job_title, :string
    add_column :companies, :admin_email, :string
    add_column :companies, :admin_telephone, :string

    add_column :companies, :admin_remark, :text

    add_column :companies, :is_suspended, :boolean, default: false


  end
end
