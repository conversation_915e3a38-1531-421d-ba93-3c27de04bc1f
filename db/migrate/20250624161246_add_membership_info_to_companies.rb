# Migration: AddMembershipInfoToCompanies
# Purpose: Adds membership-related information and additional company details
#
# This migration extends the companies table with:
# - Membership tracking (join date, identification)
# - Certificate management (issue and expiry dates)
# - Additional contact and preference information
# - Company nickname for informal identification
class AddMembershipInfoToCompanies < ActiveRecord::Migration[7.0]
  def change
    # Membership information
    add_column :companies, :join_member_at, :date              # Date when company joined as member
    add_column :companies, :identification_code, :integer      # Unique identification code for the company
    add_column :companies, :password_pain, :string             # Note: Likely meant to be "password_plain" - plain text password storage

    # Additional contact information
    add_column :companies, :contact_address, :string           # Primary contact address

    # Certificate management
    add_column :companies, :certificate_issue_at, :date        # Date when membership certificate was issued
    add_column :companies, :certificate_valid_until, :date     # Certificate expiration date

    # Preferences and additional identification
    add_column :companies, :contact_language, :string          # Preferred language for communication
    add_column :companies, :company_nick_name, :string         # Informal/short name for the company
  end
end
