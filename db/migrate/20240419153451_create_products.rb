# Migration: CreateProducts
# Purpose: Creates the products table to store product information
#
# This table supports multilingual content with separate fields for:
# - Default language (Traditional Chinese)
# - Simplified Chinese (_cn suffix)
# - English (_en suffix)
#
# The table stores comprehensive product information including:
# - GTIN (Global Trade Item Number) for barcode identification
# - Product names and descriptions in multiple languages
# - Brand information in multiple languages
# - Physical measurements (weight, content)
# - Publication status
class CreateProducts < ActiveRecord::Migration[7.0]
  def change
    create_table :products do |t|
      # Product identification
      t.string :gtin                          # Global Trade Item Number (barcode)
      t.string :trade_item_unit               # Trade item unit type

      # Product name in multiple languages
      t.string :name                          # Product name (default: Traditional Chinese)
      t.string :name_cn                       # Product name in Simplified Chinese
      t.string :name_en                       # Product name in English

      # Product description in multiple languages
      t.text :description                     # Product description (default: Traditional Chinese)
      t.text :description_cn                  # Product description in Simplified Chinese
      t.text :description_en                  # Product description in English

      # Brand information in multiple languages
      t.string :brand_name                    # Brand name (default: Traditional Chinese)
      t.string :brand_name_cn                 # Brand name in Simplified Chinese
      t.string :brand_name_en                 # Brand name in English
      t.string :brand_owner_name              # Brand owner name (default: Traditional Chinese)
      t.string :brand_owner_name_cn           # Brand owner name in Simplified Chinese
      t.string :brand_owner_name_en           # Brand owner name in English

      # Product details
      t.string :country_of_origin             # Country where product is manufactured
      t.string :internal_product_code         # Company's internal product code

      # Physical measurements
      t.float :cross_weight                   # Gross weight of product
      t.string :cross_weight_unit             # Unit of measure for gross weight (e.g., "GRM" for grams)
      t.float :net_content                    # Net content amount
      t.string :net_content_unit              # Unit of measure for net content (e.g., "MLT" for milliliters)

      # Publication status
      t.boolean :is_public_released           # Whether product is publicly visible/searchable

      # Rails standard timestamp fields
      t.timestamps                            # created_at and updated_at timestamps
    end
  end
end
