class CreateProducts < ActiveRecord::Migration[7.0]
  def change
    create_table :products do |t|
      t.string :gtin
      t.string :trade_item_unit
      t.string :name
      t.string :name_cn
      t.string :name_en
      t.text :description
      t.text :description_cn
      t.text :description_en
      t.string :brand_name
      t.string :brand_name_cn
      t.string :brand_name_en
      t.string :brand_owner_name
      t.string :brand_owner_name_cn
      t.string :brand_owner_name_en
      t.string :country_of_origin
      t.string :internal_product_code
      t.float :cross_weight
      t.string :cross_weight_unit
      t.float :net_content
      t.string :net_content_unit
      t.boolean :is_public_released

      t.timestamps
    end
  end
end
