# Database Schema for BarcodePlus 2024
# =====================================
#
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.
#
# Database Overview:
# ------------------
# This schema supports a multilingual barcode management system with the following key entities:
#
# Core Entities:
# - companies: Company information with multilingual support (zh_TW, zh_CN, en)
# - products: Product catalog with GTIN/barcode management and multilingual content
# - users: User accounts with role-based permissions and company associations
# - admins: Administrative user accounts
#
# Supporting Tables:
# - active_storage_*: File attachment support (images, documents)
# - company_prefixes: GTIN prefix assignments for companies
# - membership_payment_histories: Payment tracking for company memberships
# - industry_categories: Industry classification system
# - companies_industry_categories: Many-to-many relationship for company industries
# - versions: Application version tracking
# - lucky_numbers: Lucky number generation system
#
# Multilingual Support:
# ---------------------
# Many tables include fields with language suffixes:
# - No suffix: Default language (Traditional Chinese)
# - _cn suffix: Simplified Chinese
# - _en suffix: English
#
# Legacy Migration Support:
# -------------------------
# Tables include old_* fields to maintain traceability with the legacy system:
# - old_CMPYID, old_PDID, old_USERID: Original IDs from legacy database
# - old_LASTMDFUSER, old_LASTMDFDATE: Legacy modification tracking
# - old_CREATEDATE: Legacy creation dates

ActiveRecord::Schema[7.0].define(version: 2025_07_23_020513) do
  create_table "active_storage_attachments", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admins", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["email"], name: "index_admins_on_email", unique: true
    t.index ["reset_password_token"], name: "index_admins_on_reset_password_token", unique: true
  end

  create_table "companies", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "ipgln"
    t.string "prefix"
    t.string "name"
    t.string "name_cn"
    t.string "name_en"
    t.string "email"
    t.string "website_url"
    t.string "telephone"
    t.string "fax"
    t.string "location"
    t.string "location_cn"
    t.string "location_en"
    t.string "floor"
    t.string "floor_cn"
    t.string "floor_en"
    t.string "street"
    t.string "street_cn"
    t.string "street_en"
    t.string "city"
    t.string "city_cn"
    t.string "city_en"
    t.string "country"
    t.string "in_charge_person_name"
    t.string "in_charge_person_name_cn"
    t.string "in_charge_person_name_en"
    t.string "in_charge_job_title"
    t.string "in_charge_email"
    t.string "in_charge_telephone"
    t.string "contact_person_name"
    t.string "contact_person_name_cn"
    t.string "contact_person_name_en"
    t.string "contact_job_title"
    t.string "contact_email"
    t.string "contact_telephone"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "admin_first_name"
    t.string "admin_first_name_cn"
    t.string "admin_first_name_en"
    t.string "admin_last_name"
    t.string "admin_last_name_cn"
    t.string "admin_last_name_en"
    t.string "admin_title"
    t.string "admin_gender"
    t.string "admin_job_title"
    t.string "admin_email"
    t.string "admin_telephone"
    t.text "admin_remark"
    t.boolean "is_suspended", default: false
    t.string "old_CMPYID"
    t.string "old_LASTMDFUSER"
    t.datetime "old_LASTMDFDATE"
    t.datetime "membership_due_at"
    t.string "member_num"
    t.date "join_member_at"
    t.integer "identification_code"
    t.string "password_pain"
    t.string "contact_address"
    t.date "certificate_issue_at"
    t.date "certificate_valid_until"
    t.string "contact_language"
    t.string "company_nick_name"
  end

  create_table "companies_industry_categories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.bigint "industry_category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_companies_industry_categories_on_company_id"
    t.index ["industry_category_id"], name: "index_companies_industry_categories_on_industry_category_id"
  end

  create_table "company_prefixes", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "gcpkey"
    t.string "gcptype"
    t.bigint "company_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_company_prefixes_on_company_id"
  end

  create_table "industry_categories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "lucky_numbers", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "number"
    t.boolean "is_available", default: true
    t.string "rank"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "membership_payment_histories", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.bigint "company_id", null: false
    t.decimal "amount", precision: 10
    t.datetime "payment_date"
    t.string "payment_method"
    t.string "transaction_id"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_membership_payment_histories_on_company_id"
  end

  create_table "products", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "gtin"
    t.string "trade_item_unit"
    t.string "name"
    t.string "name_cn"
    t.string "name_en"
    t.text "description"
    t.text "description_cn"
    t.text "description_en"
    t.string "brand_name"
    t.string "brand_name_cn"
    t.string "brand_name_en"
    t.string "brand_owner_name"
    t.string "brand_owner_name_cn"
    t.string "brand_owner_name_en"
    t.string "country_of_origin"
    t.string "internal_product_code"
    t.float "cross_weight"
    t.string "cross_weight_unit"
    t.float "net_content"
    t.string "net_content_unit"
    t.boolean "is_public_released"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "company_id", null: false
    t.text "use_function"
    t.text "use_function_cn"
    t.text "use_function_en"
    t.text "direction_in_usage"
    t.text "direction_in_usage_cn"
    t.text "direction_in_usage_en"
    t.text "caution"
    t.text "caution_cn"
    t.text "caution_en"
    t.text "storage_instruction"
    t.text "storage_instruction_cn"
    t.text "storage_instruction_en"
    t.text "benefit"
    t.text "benefit_cn"
    t.text "benefit_en"
    t.text "remark"
    t.text "remark_cn"
    t.text "remark_en"
    t.boolean "is_brand_owner"
    t.boolean "is_manufacturer"
    t.string "manufacturer_gln"
    t.string "manufacturer_name"
    t.string "manufacturer_address"
    t.string "manufacturer_telephone"
    t.string "manufacturer_fax"
    t.string "manufacturer_email"
    t.string "manufacturer_website"
    t.boolean "is_distributor"
    t.string "distributor_name"
    t.string "distributor_address"
    t.string "distributor_telephone"
    t.string "distributor_fax"
    t.string "distributor_email"
    t.string "distributor_website"
    t.boolean "is_retailer"
    t.string "outer_packaging_type"
    t.integer "number_of_layers_per_carton"
    t.integer "number_of_trade_items_per_carton_layer"
    t.integer "number_of_layers_per_pailet"
    t.integer "number_of_trade_items_per_pailet_layer"
    t.integer "total_number_of_trade_items_per_pailet"
    t.decimal "price", precision: 10, scale: 2
    t.string "price_currency"
    t.decimal "basic_unit", precision: 10, scale: 2
    t.string "basic_unit_unit"
    t.decimal "discount_price", precision: 10, scale: 2
    t.string "discount_price_currency"
    t.decimal "discount_basic_unit", precision: 10, scale: 2
    t.string "discount_basic_unit_unit"
    t.boolean "is_draft", default: true
    t.string "old_PDID"
    t.string "old_LASTMDFUSER"
    t.datetime "old_LASTMDFDATE"
    t.datetime "old_CREATEDATE"
    t.string "unit_of_measure"
    t.float "depth"
    t.float "height"
    t.float "width"
    t.float "diameter"
    t.index ["company_id"], name: "index_products_on_company_id"
  end

  create_table "users", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "company_id", null: false
    t.string "old_PASSWORD"
    t.string "old_USERID"
    t.string "old_USERNAME"
    t.string "old_PASSWORDHINT"
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "locked_at"
    t.string "username"
    t.string "title"
    t.string "first_name"
    t.string "first_name_cn"
    t.string "first_name_en"
    t.string "last_name"
    t.string "last_name_cn"
    t.string "last_name_en"
    t.string "gender"
    t.string "job_title"
    t.string "tel_num"
    t.boolean "is_gtin_management", default: false
    t.boolean "is_system_admin", default: false
    t.boolean "is_user_management", default: false
    t.text "remark"
    t.index ["company_id"], name: "index_users_on_company_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "versions", charset: "utf8mb4", collation: "utf8mb4_0900_ai_ci", force: :cascade do |t|
    t.string "version"
    t.text "remarks"
    t.date "deployed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "companies_industry_categories", "companies"
  add_foreign_key "companies_industry_categories", "industry_categories"
  add_foreign_key "company_prefixes", "companies"
  add_foreign_key "membership_payment_histories", "companies"
  add_foreign_key "products", "companies"
end
