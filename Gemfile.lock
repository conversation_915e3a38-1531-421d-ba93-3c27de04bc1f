GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.8)
      actionpack (= 7.0.8)
      activesupport (= 7.0.8)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.8)
      actionpack (= 7.0.8)
      activejob (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.8)
      actionpack (= 7.0.8)
      actionview (= 7.0.8)
      activejob (= 7.0.8)
      activesupport (= 7.0.8)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.8)
      actionview (= 7.0.8)
      activesupport (= 7.0.8)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.8)
      actionpack (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.8)
      activesupport (= 7.0.8)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (7.0.8)
      activesupport (= 7.0.8)
      globalid (>= 0.3.6)
    activemodel (7.0.8)
      activesupport (= 7.0.8)
    activerecord (7.0.8)
      activemodel (= 7.0.8)
      activesupport (= 7.0.8)
    activestorage (7.0.8)
      actionpack (= 7.0.8)
      activejob (= 7.0.8)
      activerecord (= 7.0.8)
      activesupport (= 7.0.8)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.8)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    airbrussh (1.5.3)
      sshkit (>= 1.6.1, != 1.7.0)
    barby (0.6.9)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bindex (0.8.1)
    bootsnap (1.18.3)
      msgpack (~> 1.2)
    builder (3.3.0)
    capistrano (3.19.2)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.1)
      capistrano (~> 3.1)
    capistrano-rails (1.7.0)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rails-collection (0.1.0)
      capistrano-rails (~> 1.1)
    capistrano-rake (0.2.0)
      capistrano (>= 3.0)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano3-puma (5.2.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 4.0, < 6.0)
    capybara (3.39.2)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    chunky_png (1.4.0)
    concurrent-ruby (1.3.4)
    crass (1.0.6)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    erubi (1.13.0)
    ffi (1.16.3)
    globalid (1.2.1)
      activesupport (>= 6.1)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.7.2)
    irb (1.12.0)
      rdoc
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.24.1)
    msgpack (1.7.2)
    mysql2 (0.5.6)
    net-imap (0.3.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.15.6)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.2)
    pagy (6.5.0)
    psych (5.1.2)
      stringio
    public_suffix (5.0.5)
    puma (5.6.9)
      nio4r (~> 2.0)
    racc (1.8.0)
    rack (2.2.9)
    rack-mini-profiler (2.3.4)
      rack (>= 1.2.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (7.0.8)
      actioncable (= 7.0.8)
      actionmailbox (= 7.0.8)
      actionmailer (= 7.0.8)
      actionpack (= 7.0.8)
      actiontext (= 7.0.8)
      actionview (= 7.0.8)
      activejob (= 7.0.8)
      activemodel (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      bundler (>= 1.15.0)
      railties (= 7.0.8)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (7.0.8)
      actionpack (= 7.0.8)
      activesupport (= 7.0.8)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (*******)
      psych (>= 4.0.0)
    regexp_parser (2.9.0)
    reline (0.5.2)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.6)
    rqrcode (2.2.0)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    ruby-vips (2.2.1)
      ffi (~> 1.12)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    selenium-webdriver (4.9.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    spring (4.3.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.0)
      railties (>= 6.0.0)
    stringio (3.1.0)
    thor (1.3.1)
    tilt (2.6.0)
    timeout (0.4.1)
    turbo-rails (1.5.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.16)

PLATFORMS
  ruby

DEPENDENCIES
  barby
  bootsnap
  capistrano (~> 3.10)
  capistrano-rails (~> 1.3)
  capistrano-rails-collection
  capistrano-rake
  capistrano-rbenv (~> 2.1)
  capistrano3-puma (= 5.2.0)
  capybara
  chunky_png
  concurrent-ruby (= 1.3.4)
  debug
  devise (~> 4.9)
  image_processing (~> 1.2)
  importmap-rails
  jbuilder
  listen (~> 3.3)
  mysql2 (~> 0.5.6)
  pagy
  puma (~> 5.0)
  rack-mini-profiler (~> 2.0)
  rails (~> 7.0.8)
  rqrcode
  sassc-rails
  selenium-webdriver
  spring
  sprockets-rails
  stimulus-rails
  turbo-rails
  tzinfo-data
  web-console
  webdrivers

RUBY VERSION
   ruby 2.7.2p137

BUNDLED WITH
   2.1.4
