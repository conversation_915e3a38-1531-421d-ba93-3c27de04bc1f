# Use the official Ruby 2.7.2 image
FROM ruby:2.7.2

# Install essential dependencies, including <PERSON><PERSON><PERSON>'s build dependencies
RUN apt-get update -qq && apt-get install -y build-essential libmariadb-dev-compat nodejs yarn libxml2-dev libxslt1-dev libvips-dev

# Set the working directory inside the container
WORKDIR /myapp

# Configure bundler to compile <PERSON><PERSON><PERSON> from source
# This is crucial for ARM-based machines (Apple Silicon) to avoid GLIBC errors
RUN bundle config set force_ruby_platform true

# Install gems
COPY Gemfile /myapp/Gemfile
COPY Gemfile.lock /myapp/Gemfile.lock
RUN bundle install

# Copy the rest of your application's code
COPY . /myapp

# Expose port 3000 to be accessible from the host
EXPOSE 3000