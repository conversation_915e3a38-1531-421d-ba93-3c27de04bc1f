require "test_helper"

class CompaniesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @company = companies(:one)
  end

  test "should get index" do
    get companies_url
    assert_response :success
  end

  test "should get new" do
    get new_company_url
    assert_response :success
  end

  test "should create company" do
    assert_difference("Company.count") do
      post companies_url, params: { company: { city: @company.city, city_cn: @company.city_cn, city_en: @company.city_en, contact_email: @company.contact_email, contact_job_title: @company.contact_job_title, contact_person_name: @company.contact_person_name, contact_person_name_cn: @company.contact_person_name_cn, contact_person_name_en: @company.contact_person_name_en, contact_telephone: @company.contact_telephone, country: @company.country, email: @company.email, fax: @company.fax, floor: @company.floor, floor_cn: @company.floor_cn, floor_en: @company.floor_en, in_charge_email: @company.in_charge_email, in_charge_job_title: @company.in_charge_job_title, in_charge_person_name: @company.in_charge_person_name, in_charge_person_name_cn: @company.in_charge_person_name_cn, in_charge_person_name_en: @company.in_charge_person_name_en, in_charge_telephone: @company.in_charge_telephone, ipgln: @company.ipgln, location: @company.location, location_cn: @company.location_cn, location_en: @company.location_en, name: @company.name, name_cn: @company.name_cn, name_en: @company.name_en, prefix: @company.prefix, street: @company.street, street_cn: @company.street_cn, street_en: @company.street_en, telephone: @company.telephone, website_url: @company.website_url } }
    end

    assert_redirected_to company_url(Company.last)
  end

  test "should show company" do
    get company_url(@company)
    assert_response :success
  end

  test "should get edit" do
    get edit_company_url(@company)
    assert_response :success
  end

  test "should update company" do
    patch company_url(@company), params: { company: { city: @company.city, city_cn: @company.city_cn, city_en: @company.city_en, contact_email: @company.contact_email, contact_job_title: @company.contact_job_title, contact_person_name: @company.contact_person_name, contact_person_name_cn: @company.contact_person_name_cn, contact_person_name_en: @company.contact_person_name_en, contact_telephone: @company.contact_telephone, country: @company.country, email: @company.email, fax: @company.fax, floor: @company.floor, floor_cn: @company.floor_cn, floor_en: @company.floor_en, in_charge_email: @company.in_charge_email, in_charge_job_title: @company.in_charge_job_title, in_charge_person_name: @company.in_charge_person_name, in_charge_person_name_cn: @company.in_charge_person_name_cn, in_charge_person_name_en: @company.in_charge_person_name_en, in_charge_telephone: @company.in_charge_telephone, ipgln: @company.ipgln, location: @company.location, location_cn: @company.location_cn, location_en: @company.location_en, name: @company.name, name_cn: @company.name_cn, name_en: @company.name_en, prefix: @company.prefix, street: @company.street, street_cn: @company.street_cn, street_en: @company.street_en, telephone: @company.telephone, website_url: @company.website_url } }
    assert_redirected_to company_url(@company)
  end

  test "should destroy company" do
    assert_difference("Company.count", -1) do
      delete company_url(@company)
    end

    assert_redirected_to companies_url
  end
end
