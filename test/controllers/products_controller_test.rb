require "test_helper"

class ProductsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @product = products(:one)
  end

  test "should get index" do
    get products_url
    assert_response :success
  end

  test "should get new" do
    get new_product_url
    assert_response :success
  end

  test "should create product" do
    assert_difference("Product.count") do
      post products_url, params: { product: { brand_name: @product.brand_name, brand_name_cn: @product.brand_name_cn, brand_name_en: @product.brand_name_en, brand_owner_name: @product.brand_owner_name, brand_owner_name_cn: @product.brand_owner_name_cn, brand_owner_name_en: @product.brand_owner_name_en, country_of_origin: @product.country_of_origin, cross_weight: @product.cross_weight, cross_weight_unit: @product.cross_weight_unit, description: @product.description, description_cn: @product.description_cn, description_en: @product.description_en, gtin: @product.gtin, internal_product_code: @product.internal_product_code, is_public_released: @product.is_public_released, name: @product.name, name_cn: @product.name_cn, name_en: @product.name_en, net_content: @product.net_content, net_content_unit: @product.net_content_unit, trade_item_unit: @product.trade_item_unit } }
    end

    assert_redirected_to product_url(Product.last)
  end

  test "should show product" do
    get product_url(@product)
    assert_response :success
  end

  test "should get edit" do
    get edit_product_url(@product)
    assert_response :success
  end

  test "should update product" do
    patch product_url(@product), params: { product: { brand_name: @product.brand_name, brand_name_cn: @product.brand_name_cn, brand_name_en: @product.brand_name_en, brand_owner_name: @product.brand_owner_name, brand_owner_name_cn: @product.brand_owner_name_cn, brand_owner_name_en: @product.brand_owner_name_en, country_of_origin: @product.country_of_origin, cross_weight: @product.cross_weight, cross_weight_unit: @product.cross_weight_unit, description: @product.description, description_cn: @product.description_cn, description_en: @product.description_en, gtin: @product.gtin, internal_product_code: @product.internal_product_code, is_public_released: @product.is_public_released, name: @product.name, name_cn: @product.name_cn, name_en: @product.name_en, net_content: @product.net_content, net_content_unit: @product.net_content_unit, trade_item_unit: @product.trade_item_unit } }
    assert_redirected_to product_url(@product)
  end

  test "should destroy product" do
    assert_difference("Product.count", -1) do
      delete product_url(@product)
    end

    assert_redirected_to products_url
  end
end
