require "test_helper"

class LuckyNumbersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @lucky_number = lucky_numbers(:one)
  end

  test "should get index" do
    get lucky_numbers_url
    assert_response :success
  end

  test "should get new" do
    get new_lucky_number_url
    assert_response :success
  end

  test "should create lucky_number" do
    assert_difference("LuckyNumber.count") do
      post lucky_numbers_url, params: { lucky_number: { is_available: @lucky_number.is_available, number: @lucky_number.number, rank: @lucky_number.rank } }
    end

    assert_redirected_to lucky_number_url(LuckyNumber.last)
  end

  test "should show lucky_number" do
    get lucky_number_url(@lucky_number)
    assert_response :success
  end

  test "should get edit" do
    get edit_lucky_number_url(@lucky_number)
    assert_response :success
  end

  test "should update lucky_number" do
    patch lucky_number_url(@lucky_number), params: { lucky_number: { is_available: @lucky_number.is_available, number: @lucky_number.number, rank: @lucky_number.rank } }
    assert_redirected_to lucky_number_url(@lucky_number)
  end

  test "should destroy lucky_number" do
    assert_difference("LuckyNumber.count", -1) do
      delete lucky_number_url(@lucky_number)
    end

    assert_redirected_to lucky_numbers_url
  end
end
