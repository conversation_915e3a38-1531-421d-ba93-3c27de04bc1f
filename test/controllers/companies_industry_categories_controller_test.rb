require "test_helper"

class CompaniesIndustryCategoriesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @companies_industry_category = companies_industry_categories(:one)
  end

  test "should get index" do
    get companies_industry_categories_url
    assert_response :success
  end

  test "should get new" do
    get new_companies_industry_category_url
    assert_response :success
  end

  test "should create companies_industry_category" do
    assert_difference("CompaniesIndustryCategory.count") do
      post companies_industry_categories_url, params: { companies_industry_category: { company_id: @companies_industry_category.company_id, industry_category_id: @companies_industry_category.industry_category_id } }
    end

    assert_redirected_to companies_industry_category_url(CompaniesIndustryCategory.last)
  end

  test "should show companies_industry_category" do
    get companies_industry_category_url(@companies_industry_category)
    assert_response :success
  end

  test "should get edit" do
    get edit_companies_industry_category_url(@companies_industry_category)
    assert_response :success
  end

  test "should update companies_industry_category" do
    patch companies_industry_category_url(@companies_industry_category), params: { companies_industry_category: { company_id: @companies_industry_category.company_id, industry_category_id: @companies_industry_category.industry_category_id } }
    assert_redirected_to companies_industry_category_url(@companies_industry_category)
  end

  test "should destroy companies_industry_category" do
    assert_difference("CompaniesIndustryCategory.count", -1) do
      delete companies_industry_category_url(@companies_industry_category)
    end

    assert_redirected_to companies_industry_categories_url
  end
end
