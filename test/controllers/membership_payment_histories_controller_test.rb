require "test_helper"

class MembershipPaymentHistoriesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @membership_payment_history = membership_payment_histories(:one)
  end

  test "should get index" do
    get membership_payment_histories_url
    assert_response :success
  end

  test "should get new" do
    get new_membership_payment_history_url
    assert_response :success
  end

  test "should create membership_payment_history" do
    assert_difference("MembershipPaymentHistory.count") do
      post membership_payment_histories_url, params: { membership_payment_history: { amount: @membership_payment_history.amount, company_id: @membership_payment_history.company_id, notes: @membership_payment_history.notes, payment_date: @membership_payment_history.payment_date, payment_method: @membership_payment_history.payment_method, transaction_id: @membership_payment_history.transaction_id } }
    end

    assert_redirected_to membership_payment_history_url(MembershipPaymentHistory.last)
  end

  test "should show membership_payment_history" do
    get membership_payment_history_url(@membership_payment_history)
    assert_response :success
  end

  test "should get edit" do
    get edit_membership_payment_history_url(@membership_payment_history)
    assert_response :success
  end

  test "should update membership_payment_history" do
    patch membership_payment_history_url(@membership_payment_history), params: { membership_payment_history: { amount: @membership_payment_history.amount, company_id: @membership_payment_history.company_id, notes: @membership_payment_history.notes, payment_date: @membership_payment_history.payment_date, payment_method: @membership_payment_history.payment_method, transaction_id: @membership_payment_history.transaction_id } }
    assert_redirected_to membership_payment_history_url(@membership_payment_history)
  end

  test "should destroy membership_payment_history" do
    assert_difference("MembershipPaymentHistory.count", -1) do
      delete membership_payment_history_url(@membership_payment_history)
    end

    assert_redirected_to membership_payment_histories_url
  end
end
