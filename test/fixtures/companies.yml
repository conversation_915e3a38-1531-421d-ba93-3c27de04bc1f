# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  ipgln: MyString
  prefix: MyString
  name: MyString
  name_cn: MyString
  name_en: MyString
  email: MyString
  website_url: MyString
  telephone: MyString
  fax: MyString
  location: MyString
  location_cn: MyString
  location_en: MyString
  floor: MyString
  floor_cn: MyString
  floor_en: MyString
  street: MyString
  street_cn: MyString
  street_en: MyString
  city: MyString
  city_cn: MyString
  city_en: MyString
  country: MyString
  in_charge_person_name: MyString
  in_charge_person_name_cn: MyString
  in_charge_person_name_en: MyString
  in_charge_job_title: MyString
  in_charge_email: MyString
  in_charge_telephone: MyString
  contact_person_name: MyString
  contact_person_name_cn: MyString
  contact_person_name_en: MyString
  contact_job_title: MyString
  contact_email: MyString
  contact_telephone: MyString

two:
  ipgln: MyString
  prefix: MyString
  name: MyString
  name_cn: MyString
  name_en: MyString
  email: MyString
  website_url: MyString
  telephone: MyString
  fax: MyString
  location: MyString
  location_cn: MyString
  location_en: MyString
  floor: MyString
  floor_cn: MyString
  floor_en: MyString
  street: MyString
  street_cn: MyString
  street_en: MyString
  city: MyString
  city_cn: MyString
  city_en: MyString
  country: MyString
  in_charge_person_name: MyString
  in_charge_person_name_cn: MyString
  in_charge_person_name_en: MyString
  in_charge_job_title: MyString
  in_charge_email: MyString
  in_charge_telephone: MyString
  contact_person_name: MyString
  contact_person_name_cn: MyString
  contact_person_name_en: MyString
  contact_job_title: MyString
  contact_email: MyString
  contact_telephone: MyString
