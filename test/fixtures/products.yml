# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  gtin: MyString
  trade_item_unit: MyString
  name: MyString
  name_cn: MyString
  name_en: MyString
  description: MyText
  description_cn: MyText
  description_en: MyText
  brand_name: MyString
  brand_name_cn: MyString
  brand_name_en: MyString
  brand_owner_name: MyString
  brand_owner_name_cn: MyString
  brand_owner_name_en: MyString
  country_of_origin: MyString
  internal_product_code: MyString
  cross_weight: 1.5
  cross_weight_unit: MyString
  net_content: 1.5
  net_content_unit: MyString
  is_public_released: false

two:
  gtin: MyString
  trade_item_unit: MyString
  name: MyString
  name_cn: MyString
  name_en: MyString
  description: MyText
  description_cn: MyText
  description_en: MyText
  brand_name: MyString
  brand_name_cn: MyString
  brand_name_en: MyString
  brand_owner_name: MyString
  brand_owner_name_cn: MyString
  brand_owner_name_en: MyString
  country_of_origin: MyString
  internal_product_code: MyString
  cross_weight: 1.5
  cross_weight_unit: MyString
  net_content: 1.5
  net_content_unit: MyString
  is_public_released: false
