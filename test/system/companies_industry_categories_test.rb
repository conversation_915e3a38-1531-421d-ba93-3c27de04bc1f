require "application_system_test_case"

class CompaniesIndustryCategoriesTest < ApplicationSystemTestCase
  setup do
    @companies_industry_category = companies_industry_categories(:one)
  end

  test "visiting the index" do
    visit companies_industry_categories_url
    assert_selector "h1", text: "Companies industry categories"
  end

  test "should create companies industry category" do
    visit companies_industry_categories_url
    click_on "New companies industry category"

    fill_in "Company", with: @companies_industry_category.company_id
    fill_in "Industry category", with: @companies_industry_category.industry_category_id
    click_on "Create Companies industry category"

    assert_text "Companies industry category was successfully created"
    click_on "Back"
  end

  test "should update Companies industry category" do
    visit companies_industry_category_url(@companies_industry_category)
    click_on "Edit this companies industry category", match: :first

    fill_in "Company", with: @companies_industry_category.company_id
    fill_in "Industry category", with: @companies_industry_category.industry_category_id
    click_on "Update Companies industry category"

    assert_text "Companies industry category was successfully updated"
    click_on "Back"
  end

  test "should destroy Companies industry category" do
    visit companies_industry_category_url(@companies_industry_category)
    click_on "Destroy this companies industry category", match: :first

    assert_text "Companies industry category was successfully destroyed"
  end
end
