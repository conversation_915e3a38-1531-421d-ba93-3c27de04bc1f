require "application_system_test_case"

class MembershipPaymentHistoriesTest < ApplicationSystemTestCase
  setup do
    @membership_payment_history = membership_payment_histories(:one)
  end

  test "visiting the index" do
    visit membership_payment_histories_url
    assert_selector "h1", text: "Membership payment histories"
  end

  test "should create membership payment history" do
    visit membership_payment_histories_url
    click_on "New membership payment history"

    fill_in "Amount", with: @membership_payment_history.amount
    fill_in "Company", with: @membership_payment_history.company_id
    fill_in "Notes", with: @membership_payment_history.notes
    fill_in "Payment date", with: @membership_payment_history.payment_date
    fill_in "Payment method", with: @membership_payment_history.payment_method
    fill_in "Transaction", with: @membership_payment_history.transaction_id
    click_on "Create Membership payment history"

    assert_text "Membership payment history was successfully created"
    click_on "Back"
  end

  test "should update Membership payment history" do
    visit membership_payment_history_url(@membership_payment_history)
    click_on "Edit this membership payment history", match: :first

    fill_in "Amount", with: @membership_payment_history.amount
    fill_in "Company", with: @membership_payment_history.company_id
    fill_in "Notes", with: @membership_payment_history.notes
    fill_in "Payment date", with: @membership_payment_history.payment_date
    fill_in "Payment method", with: @membership_payment_history.payment_method
    fill_in "Transaction", with: @membership_payment_history.transaction_id
    click_on "Update Membership payment history"

    assert_text "Membership payment history was successfully updated"
    click_on "Back"
  end

  test "should destroy Membership payment history" do
    visit membership_payment_history_url(@membership_payment_history)
    click_on "Destroy this membership payment history", match: :first

    assert_text "Membership payment history was successfully destroyed"
  end
end
