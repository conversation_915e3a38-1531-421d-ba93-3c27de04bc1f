require "application_system_test_case"

class ProductsTest < ApplicationSystemTestCase
  setup do
    @product = products(:one)
  end

  test "visiting the index" do
    visit products_url
    assert_selector "h1", text: "Products"
  end

  test "should create product" do
    visit products_url
    click_on "New product"

    fill_in "Brand name", with: @product.brand_name
    fill_in "Brand name cn", with: @product.brand_name_cn
    fill_in "Brand name en", with: @product.brand_name_en
    fill_in "Brand owner name", with: @product.brand_owner_name
    fill_in "Brand owner name cn", with: @product.brand_owner_name_cn
    fill_in "Brand owner name en", with: @product.brand_owner_name_en
    fill_in "Country of origin", with: @product.country_of_origin
    fill_in "Cross weight", with: @product.cross_weight
    fill_in "Cross weight unit", with: @product.cross_weight_unit
    fill_in "Description", with: @product.description
    fill_in "Description cn", with: @product.description_cn
    fill_in "Description en", with: @product.description_en
    fill_in "Gtin", with: @product.gtin
    fill_in "Internal product code", with: @product.internal_product_code
    check "Is public released" if @product.is_public_released
    fill_in "Name", with: @product.name
    fill_in "Name cn", with: @product.name_cn
    fill_in "Name en", with: @product.name_en
    fill_in "Net content", with: @product.net_content
    fill_in "Net content unit", with: @product.net_content_unit
    fill_in "Trade item unit", with: @product.trade_item_unit
    click_on "Create Product"

    assert_text "Product was successfully created"
    click_on "Back"
  end

  test "should update Product" do
    visit product_url(@product)
    click_on "Edit this product", match: :first

    fill_in "Brand name", with: @product.brand_name
    fill_in "Brand name cn", with: @product.brand_name_cn
    fill_in "Brand name en", with: @product.brand_name_en
    fill_in "Brand owner name", with: @product.brand_owner_name
    fill_in "Brand owner name cn", with: @product.brand_owner_name_cn
    fill_in "Brand owner name en", with: @product.brand_owner_name_en
    fill_in "Country of origin", with: @product.country_of_origin
    fill_in "Cross weight", with: @product.cross_weight
    fill_in "Cross weight unit", with: @product.cross_weight_unit
    fill_in "Description", with: @product.description
    fill_in "Description cn", with: @product.description_cn
    fill_in "Description en", with: @product.description_en
    fill_in "Gtin", with: @product.gtin
    fill_in "Internal product code", with: @product.internal_product_code
    check "Is public released" if @product.is_public_released
    fill_in "Name", with: @product.name
    fill_in "Name cn", with: @product.name_cn
    fill_in "Name en", with: @product.name_en
    fill_in "Net content", with: @product.net_content
    fill_in "Net content unit", with: @product.net_content_unit
    fill_in "Trade item unit", with: @product.trade_item_unit
    click_on "Update Product"

    assert_text "Product was successfully updated"
    click_on "Back"
  end

  test "should destroy Product" do
    visit product_url(@product)
    click_on "Destroy this product", match: :first

    assert_text "Product was successfully destroyed"
  end
end
