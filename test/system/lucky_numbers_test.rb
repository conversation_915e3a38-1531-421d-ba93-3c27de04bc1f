require "application_system_test_case"

class LuckyNumbersTest < ApplicationSystemTestCase
  setup do
    @lucky_number = lucky_numbers(:one)
  end

  test "visiting the index" do
    visit lucky_numbers_url
    assert_selector "h1", text: "Lucky numbers"
  end

  test "should create lucky number" do
    visit lucky_numbers_url
    click_on "New lucky number"

    check "Is available" if @lucky_number.is_available
    fill_in "Number", with: @lucky_number.number
    fill_in "Rank", with: @lucky_number.rank
    click_on "Create Lucky number"

    assert_text "Lucky number was successfully created"
    click_on "Back"
  end

  test "should update Lucky number" do
    visit lucky_number_url(@lucky_number)
    click_on "Edit this lucky number", match: :first

    check "Is available" if @lucky_number.is_available
    fill_in "Number", with: @lucky_number.number
    fill_in "Rank", with: @lucky_number.rank
    click_on "Update Lucky number"

    assert_text "Lucky number was successfully updated"
    click_on "Back"
  end

  test "should destroy Lucky number" do
    visit lucky_number_url(@lucky_number)
    click_on "Destroy this lucky number", match: :first

    assert_text "Lucky number was successfully destroyed"
  end
end
