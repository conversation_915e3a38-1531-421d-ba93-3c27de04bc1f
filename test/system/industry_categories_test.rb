require "application_system_test_case"

class IndustryCategoriesTest < ApplicationSystemTestCase
  setup do
    @industry_category = industry_categories(:one)
  end

  test "visiting the index" do
    visit industry_categories_url
    assert_selector "h1", text: "Industry categories"
  end

  test "should create industry category" do
    visit industry_categories_url
    click_on "New industry category"

    fill_in "Name", with: @industry_category.name
    click_on "Create Industry category"

    assert_text "Industry category was successfully created"
    click_on "Back"
  end

  test "should update Industry category" do
    visit industry_category_url(@industry_category)
    click_on "Edit this industry category", match: :first

    fill_in "Name", with: @industry_category.name
    click_on "Update Industry category"

    assert_text "Industry category was successfully updated"
    click_on "Back"
  end

  test "should destroy Industry category" do
    visit industry_category_url(@industry_category)
    click_on "Destroy this industry category", match: :first

    assert_text "Industry category was successfully destroyed"
  end
end
