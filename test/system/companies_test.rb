require "application_system_test_case"

class CompaniesTest < ApplicationSystemTestCase
  setup do
    @company = companies(:one)
  end

  test "visiting the index" do
    visit companies_url
    assert_selector "h1", text: "Companies"
  end

  test "should create company" do
    visit companies_url
    click_on "New company"

    fill_in "City", with: @company.city
    fill_in "City cn", with: @company.city_cn
    fill_in "City en", with: @company.city_en
    fill_in "Contact email", with: @company.contact_email
    fill_in "Contact job title", with: @company.contact_job_title
    fill_in "Contact person name", with: @company.contact_person_name
    fill_in "Contact person name cn", with: @company.contact_person_name_cn
    fill_in "Contact person name en", with: @company.contact_person_name_en
    fill_in "Contact telephone", with: @company.contact_telephone
    fill_in "Country", with: @company.country
    fill_in "Email", with: @company.email
    fill_in "Fax", with: @company.fax
    fill_in "Floor", with: @company.floor
    fill_in "Floor cn", with: @company.floor_cn
    fill_in "Floor en", with: @company.floor_en
    fill_in "In charge email", with: @company.in_charge_email
    fill_in "In charge job title", with: @company.in_charge_job_title
    fill_in "In charge person name", with: @company.in_charge_person_name
    fill_in "In charge person name cn", with: @company.in_charge_person_name_cn
    fill_in "In charge person name en", with: @company.in_charge_person_name_en
    fill_in "In charge telephone", with: @company.in_charge_telephone
    fill_in "Ipgln", with: @company.ipgln
    fill_in "Location", with: @company.location
    fill_in "Location cn", with: @company.location_cn
    fill_in "Location en", with: @company.location_en
    fill_in "Name", with: @company.name
    fill_in "Name cn", with: @company.name_cn
    fill_in "Name en", with: @company.name_en
    fill_in "Prefix", with: @company.prefix
    fill_in "Street", with: @company.street
    fill_in "Street cn", with: @company.street_cn
    fill_in "Street en", with: @company.street_en
    fill_in "Telephone", with: @company.telephone
    fill_in "Website url", with: @company.website_url
    click_on "Create Company"

    assert_text "Company was successfully created"
    click_on "Back"
  end

  test "should update Company" do
    visit company_url(@company)
    click_on "Edit this company", match: :first

    fill_in "City", with: @company.city
    fill_in "City cn", with: @company.city_cn
    fill_in "City en", with: @company.city_en
    fill_in "Contact email", with: @company.contact_email
    fill_in "Contact job title", with: @company.contact_job_title
    fill_in "Contact person name", with: @company.contact_person_name
    fill_in "Contact person name cn", with: @company.contact_person_name_cn
    fill_in "Contact person name en", with: @company.contact_person_name_en
    fill_in "Contact telephone", with: @company.contact_telephone
    fill_in "Country", with: @company.country
    fill_in "Email", with: @company.email
    fill_in "Fax", with: @company.fax
    fill_in "Floor", with: @company.floor
    fill_in "Floor cn", with: @company.floor_cn
    fill_in "Floor en", with: @company.floor_en
    fill_in "In charge email", with: @company.in_charge_email
    fill_in "In charge job title", with: @company.in_charge_job_title
    fill_in "In charge person name", with: @company.in_charge_person_name
    fill_in "In charge person name cn", with: @company.in_charge_person_name_cn
    fill_in "In charge person name en", with: @company.in_charge_person_name_en
    fill_in "In charge telephone", with: @company.in_charge_telephone
    fill_in "Ipgln", with: @company.ipgln
    fill_in "Location", with: @company.location
    fill_in "Location cn", with: @company.location_cn
    fill_in "Location en", with: @company.location_en
    fill_in "Name", with: @company.name
    fill_in "Name cn", with: @company.name_cn
    fill_in "Name en", with: @company.name_en
    fill_in "Prefix", with: @company.prefix
    fill_in "Street", with: @company.street
    fill_in "Street cn", with: @company.street_cn
    fill_in "Street en", with: @company.street_en
    fill_in "Telephone", with: @company.telephone
    fill_in "Website url", with: @company.website_url
    click_on "Update Company"

    assert_text "Company was successfully updated"
    click_on "Back"
  end

  test "should destroy Company" do
    visit company_url(@company)
    click_on "Destroy this company", match: :first

    assert_text "Company was successfully destroyed"
  end
end
