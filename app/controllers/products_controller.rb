class ProductsController < ApplicationController

  before_action :authenticate_user!
  before_action :set_product, only: %i[ show edit update destroy ]

  # GET /products or /products.json
  def index
    @pagy, @products = pagy(Product.search(params[:query]))
  end

  # GET /products/1 or /products/1.json
  def show
  end

  def show_gtin
    @gtin = params[:gtin]

    redirect_to "https://id.gs1mo.org/01/#{@gtin}", allow_other_host: true
    return


    # TODO: in the future, use this rendering instead of redirection.
    @gtin = @gtin.sub(/^0/, '')
    @product = Product.find_by(gtin: @gtin)
    if @product.nil?
      render :not_found
      return
    end
  end

  # GET /products/new
  def new
    @product = Product.new
    @product.company_id = params[:company_id]
    if @product.company_id.nil?
      @product.company_id = current_user.company.id
    end
    @last_gtin = Product.last_gtin(@product.company_id)
    if @last_gtin.present?
      @product.gtin = @last_gtin.next.to_s
    end
  end

  # GET /products/1/edit
  def edit
  end

  # POST /products or /products.json
  def create
    @product = Product.new(product_params)
    # @product.company = current_user.company

    respond_to do |format|
      if @product.save
        format.html { redirect_to product_url(@product), notice: "Product was successfully created." }
        format.json { render :show, status: :created, location: @product }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /products/1 or /products/1.json
  def update
    respond_to do |format|
      if @product.update(product_params)
        format.html { redirect_to product_url(@product), notice: "Product was successfully updated." }
        format.json { render :show, status: :ok, location: @product }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /products/1 or /products/1.json
  def destroy
    @product.destroy

    respond_to do |format|
      format.html { redirect_to products_url, notice: "Product was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_product
      @product = Product.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def product_params
      params.require(:product).permit(:company_id,
                                      :is_draft,
                                      :gtin,
                                      :trade_item_unit,
                                      :name,
                                      :name_cn,
                                      :name_en,
                                      :description,
                                      :description_cn,
                                      :description_en,
                                      :brand_name,
                                      :brand_name_cn,
                                      :brand_name_en,
                                      :brand_owner_name,
                                      :brand_owner_name_cn,
                                      :brand_owner_name_en,
                                      :country_of_origin,
                                      :internal_product_code,
                                      :cross_weight,
                                      :cross_weight_unit,
                                      :net_content,
                                      :net_content_unit,
                                      :is_public_released,
                                      :cover_image,
                                      :use_function,
                                      :use_function_cn,
                                      :use_function_en,
                                      :direction_in_usage,
                                      :direction_in_usage_cn,
                                      :direction_in_usage_en,
                                      :caution,
                                      :caution_cn,
                                      :caution_en,
                                      :storage_instruction,
                                      :storage_instruction_cn,
                                      :storage_instruction_en,
                                      :benefit,
                                      :benefit_cn,
                                      :benefit_en,
                                      :remark,
                                      :remark_cn,
                                      :remark_en,
                                      :is_brand_owner,
                                      :is_manufacturer,
                                      :manufacturer_gln,
                                      :manufacturer_name,
                                      :manufacturer_address,
                                      :manufacturer_telephone,
                                      :manufacturer_fax,
                                      :manufacturer_email,
                                      :manufacturer_website,
                                      :is_distributor,
                                      :distributor_name,
                                      :distributor_address,
                                      :distributor_telephone,
                                      :distributor_fax,
                                      :distributor_email,
                                      :distributor_website,
                                      :is_retailer,
                                      :outer_packaging_type,
                                      :number_of_layers_per_carton,
                                      :number_of_trade_items_per_carton_layer,
                                      :number_of_layers_per_pailet,
                                      :number_of_trade_items_per_pailet_layer,
                                      :total_number_of_trade_items_per_pailet,
                                      :price,
                                      :price_currency,
                                      :basic_unit,
                                      :basic_unit_unit,
                                      :discount_price,
                                      :discount_price_currency,
                                      :discount_basic_unit,
                                      :discount_basic_unit_unit,
                                      images: [])
    end
end
