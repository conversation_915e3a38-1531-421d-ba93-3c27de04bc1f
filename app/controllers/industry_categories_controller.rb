class IndustryCategoriesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_industry_category, only: %i[ show edit update destroy ]

  # GET /industry_categories or /industry_categories.json
  def index
    @industry_categories = IndustryCategory.all
  end

  # GET /industry_categories/1 or /industry_categories/1.json
  def show
  end

  # GET /industry_categories/new
  def new
    @industry_category = IndustryCategory.new
  end

  # GET /industry_categories/1/edit
  def edit
  end

  # POST /industry_categories or /industry_categories.json
  def create
    @industry_category = IndustryCategory.new(industry_category_params)

    respond_to do |format|
      if @industry_category.save
        format.html { redirect_to industry_category_url(@industry_category), notice: "Industry category was successfully created." }
        format.json { render :show, status: :created, location: @industry_category }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @industry_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /industry_categories/1 or /industry_categories/1.json
  def update
    respond_to do |format|
      if @industry_category.update(industry_category_params)
        format.html { redirect_to industry_category_url(@industry_category), notice: "Industry category was successfully updated." }
        format.json { render :show, status: :ok, location: @industry_category }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @industry_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /industry_categories/1 or /industry_categories/1.json
  def destroy
    @industry_category.destroy

    respond_to do |format|
      format.html { redirect_to industry_categories_url, notice: "Industry category was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_industry_category
      @industry_category = IndustryCategory.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def industry_category_params
      params.require(:industry_category).permit(:name)
    end
end
