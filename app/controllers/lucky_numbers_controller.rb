class LuckyNumbersController < ApplicationController
  before_action :authenticate_user!
  before_action :set_lucky_number, only: %i[ show edit update destroy ]

  # GET /lucky_numbers or /lucky_numbers.json
  def index
    @lucky_numbers = LuckyNumber.all
  end

  # GET /lucky_numbers/1 or /lucky_numbers/1.json
  def show
  end

  # GET /lucky_numbers/new
  def new
    @lucky_number = LuckyNumber.new
  end

  # GET /lucky_numbers/1/edit
  def edit
  end

  # POST /lucky_numbers or /lucky_numbers.json
  def create
    @lucky_number = LuckyNumber.new(lucky_number_params)

    respond_to do |format|
      if @lucky_number.save
        format.html { redirect_to lucky_number_url(@lucky_number), notice: "Lucky number was successfully created." }
        format.json { render :show, status: :created, location: @lucky_number }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @lucky_number.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /lucky_numbers/1 or /lucky_numbers/1.json
  def update
    respond_to do |format|
      if @lucky_number.update(lucky_number_params)
        format.html { redirect_to lucky_number_url(@lucky_number), notice: "Lucky number was successfully updated." }
        format.json { render :show, status: :ok, location: @lucky_number }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @lucky_number.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /lucky_numbers/1 or /lucky_numbers/1.json
  def destroy
    @lucky_number.destroy

    respond_to do |format|
      format.html { redirect_to lucky_numbers_url, notice: "Lucky number was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_lucky_number
      @lucky_number = LuckyNumber.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def lucky_number_params
      params.require(:lucky_number).permit(:number, :is_available, :rank)
    end
end
