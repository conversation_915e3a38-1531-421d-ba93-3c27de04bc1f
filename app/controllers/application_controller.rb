class ApplicationController < ActionController::Base

    include Pagy::Backend
    Pagy::DEFAULT[:items] = 50

    around_action :switch_locale

    def switch_locale(&action)
        if params[:locale] && I18n.available_locales.include?(params[:locale].to_sym)
            locale = params[:locale]
            session[:locale] = params[:locale]
        else
            locale = I18n.locale = session[:locale] || I18n.default_locale
        end

        I18n.with_locale(locale, &action)
    end

end
