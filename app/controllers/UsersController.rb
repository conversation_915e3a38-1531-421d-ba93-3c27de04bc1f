class UsersController < ApplicationController

  before_action :authenticate_user!
  before_action :set_user, only: %i[ show edit update]

  def index
    if params[:query].present?
      @users = User.search(params[:query]).order(:company_id)
    else
      @users = User.all.order(:company_id)
    end
    @pagy, @users = pagy(@users)
  end

  def new
    @user = User.new
  end

  def edit
  end

  def update
    respond_to do |format|
      if @user.update(user_params)
        format.html { redirect_to user_url(@user), notice: "User was successfully updated." }
        format.json { render :show, status: :ok, location: @user }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @user.errors, status: :unprocessable_entity }
      end
    end
  end

  def show
  end

  private

    def set_user
      @user = User.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def user_params
      params.require(:user).permit(:email,
                                      :username,
                                      :title,
                                      :first_name,
                                      :first_name_cn,
                                      :first_name_en,
                                      :last_name,
                                      :last_name_cn,
                                      :last_name_en,
                                      :gender,
                                      :job_title,
                                      :tel_num,
                                      :is_gtin_management,
                                      :is_system_admin,
                                      :is_user_management,
                                      :remark
      )
    end

end