class CompaniesController < ApplicationController

  before_action :authenticate_user!
  before_action :set_company, only: %i[ show edit update destroy ]

  # GET /companies or /companies.json
  def index
    @status_filter = params[:status]
    @sort_by = params[:sort_by] || 'ipgln_asc'

    companies = Company.search(params[:query])

    # Apply status filter
    if @status_filter.present? && %w[active inactive].include?(@status_filter)
      today = Date.current
      if @status_filter == 'active'
        companies = companies.where('membership_due_at >= ?', today)
      else
        companies = companies.where('membership_due_at IS NULL OR membership_due_at < ?', today)
      end
    end

    # Apply sorting
    case @sort_by
    when 'expiry_asc'
      companies = companies.order(Arel.sql('membership_due_at IS NULL, membership_due_at ASC'))
    when 'expiry_desc'
      companies = companies.order(Arel.sql('membership_due_at IS NULL, membership_due_at DESC'))
    when 'name_desc'
      companies = companies.order(name: :desc)
    when 'ipgln_asc'
      companies = companies.order(ipgln: :asc)
    when 'ipgln_desc'
      companies = companies.order(ipgln: :desc)
    else # name_asc
      companies = companies.order(name: :asc)
    end

    @pagy, @companies = pagy(companies)
  end

  def prefixes
    @companies = Company.all.order(prefix: :asc)
  end

  def random_new_prefix
    @prefix_length = params[:length]|| 6
    if params[:prefix].present?
      @prefix = params[:prefix]
      @prefix_length = @prefix.length-3
    else
      @prefix = Company.generate_random_prefix(@prefix_length.to_i)
    end

    @temp_company = Company.new(prefix: @prefix)

    @total_prefixes = CompanyPrefix.count + Company.where.not(prefix: nil).count
    # Check the prefix from Company, and the prefix less 1 digit, and less 2 digits, until only 4 digits left.
    if @companies.nil?
      @companies = []
      @companies = Company.where("prefix LIKE ?", "#{@prefix}%").order(prefix: :asc)
      (4..@prefix_length.to_i).each do |length|
        @companies += Company.where(prefix: "#{@prefix[0...length]}")
      end

      @companies.to_a.uniq!
    end

    if params[:list_all] == "true"
      @companies = Company.where.not(prefix: nil).order(prefix: :asc)
    end
  end

  # GET /companies/1 or /companies/1.json
  def show
  end

  # GET /companies/new
  def new
    @company = Company.new
    @company.company_prefixes.build
  end

  # GET /companies/1/edit
  def edit
  end

  def edit_membership
    @company = Company.find(params[:id])
  end

  # POST /companies or /companies.json
  def create
    @company = Company.new(company_params)

    respond_to do |format|
      if @company.save
        format.html { redirect_to company_url(@company), notice: "Company was successfully created." }
        format.json { render :show, status: :created, location: @company }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @company.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /companies/1 or /companies/1.json
  def update
    respond_to do |format|
      if @company.update(company_params)
        format.html { redirect_to company_url(@company), notice: "Company was successfully updated." }
        format.json { render :show, status: :ok, location: @company }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @company.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /companies/1 or /companies/1.json
  def destroy
    @company.destroy

    respond_to do |format|
      format.html { redirect_to companies_url, notice: "Company was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_company
      @company = Company.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def company_params
      params.require(:company).permit(:ipgln,
                                      :prefix,
                                      :name,
                                      :name_cn,
                                      :name_en,
                                      :email,
                                      :website_url,
                                      :telephone,
                                      :fax,
                                      :location,
                                      :location_cn,
                                      :location_en,
                                      :floor,
                                      :floor_cn,
                                      :floor_en,
                                      :street,
                                      :street_cn,
                                      :street_en,
                                      :city,
                                      :city_cn,
                                      :city_en,
                                      :country,
                                      :in_charge_person_name,
                                      :in_charge_person_name_cn,
                                      :in_charge_person_name_en,
                                      :in_charge_job_title,
                                      :in_charge_email,
                                      :in_charge_telephone,
                                      :contact_person_name,
                                      :contact_person_name_cn,
                                      :contact_person_name_en,
                                      :contact_job_title,
                                      :contact_email,
                                      :contact_telephone,
                                      :admin_first_name,
                                      :admin_first_name_cn,
                                      :admin_first_name_en,
                                      :admin_last_name,
                                      :admin_last_name_cn,
                                      :admin_last_name_en,
                                      :admin_title,
                                      :admin_gender,
                                      :admin_job_title,
                                      :admin_email,
                                      :admin_telephone,
                                      :admin_remark,
                                      :is_suspended,
                                      # Membership
                                      :member_num,
                                      :join_member_at,
                                      :identification_code,
                                      :password_pain,
                                      :contact_address,
                                      :certificate_issue_at,
                                      :certificate_valid_until,
                                      :contact_language,
                                      :company_nick_name,
                                      # Others
                                      industry_category_ids: [],
                                      company_prefixes_attributes: [:id, :gcpkey, :gcptype, :_destroy])
    end
end
