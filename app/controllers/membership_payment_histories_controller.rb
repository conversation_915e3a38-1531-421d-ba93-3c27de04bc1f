class MembershipPaymentHistoriesController < ApplicationController
  before_action :set_company, except: [:index, :show, :edit, :update, :destroy]
  before_action :set_membership_payment_history, only: %i[show edit update destroy]

  # GET /companies/:company_id/membership_payment_histories
  def index
    if params[:company_id].present?
      @company = Company.find(params[:company_id])
      @membership_payment_histories = @company.membership_payment_histories.order(payment_date: :desc)
    else
      @membership_payment_histories = MembershipPaymentHistory.all.order(payment_date: :desc)
    end
  end

  # GET /membership_payment_histories/1
  def show
  end

  # GET /companies/:company_id/membership_payment_histories/new
  def new
    @membership_payment_history = @company.membership_payment_histories.new(
      payment_date: Date.current,
      amount: 2600 # Default amount, adjust as needed
    )
  end

  # GET /membership_payment_histories/1/edit
  def edit
  end

  # POST /companies/:company_id/membership_payment_histories
  def create
    @membership_payment_history = @company.membership_payment_histories.new(membership_payment_history_params)

    respond_to do |format|
      if @membership_payment_history.save
        format.html { redirect_to company_path(@company), notice: "Membership payment was successfully recorded." }
        format.json { render :show, status: :created, location: @membership_payment_history }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @membership_payment_history.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /membership_payment_histories/1
  def update
    respond_to do |format|
      if @membership_payment_history.update(membership_payment_history_params)
        format.html { redirect_to company_path(@membership_payment_history.company), notice: "Membership payment was successfully updated." }
        format.json { render :show, status: :ok, location: @membership_payment_history }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @membership_payment_history.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /membership_payment_histories/1
  def destroy
    company = @membership_payment_history.company
    @membership_payment_history.destroy!

    respond_to do |format|
      format.html { redirect_to company_path(company), notice: "Membership payment was successfully deleted." }
      format.json { head :no_content }
    end
  end

  private
    # Set the company for nested routes
    def set_company
      @company = Company.find(params[:company_id])
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_membership_payment_history
      @membership_payment_history = MembershipPaymentHistory.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def membership_payment_history_params
      params.require(:membership_payment_history).permit(:company_id, :amount, :payment_date, :payment_method, :transaction_id, :notes)
    end
end
