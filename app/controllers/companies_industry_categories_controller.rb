class CompaniesIndustryCategoriesController < ApplicationController
  before_action :authenticate_user!
  before_action :set_companies_industry_category, only: %i[ show edit update destroy ]

  # GET /companies_industry_categories or /companies_industry_categories.json
  def index
    @companies_industry_categories = CompaniesIndustryCategory.all
  end

  # GET /companies_industry_categories/1 or /companies_industry_categories/1.json
  def show
  end

  # GET /companies_industry_categories/new
  def new
    @companies_industry_category = CompaniesIndustryCategory.new
  end

  # GET /companies_industry_categories/1/edit
  def edit
  end

  # POST /companies_industry_categories or /companies_industry_categories.json
  def create
    @companies_industry_category = CompaniesIndustryCategory.new(companies_industry_category_params)

    respond_to do |format|
      if @companies_industry_category.save
        format.html { redirect_to companies_industry_category_url(@companies_industry_category), notice: "Companies industry category was successfully created." }
        format.json { render :show, status: :created, location: @companies_industry_category }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @companies_industry_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /companies_industry_categories/1 or /companies_industry_categories/1.json
  def update
    respond_to do |format|
      if @companies_industry_category.update(companies_industry_category_params)
        format.html { redirect_to companies_industry_category_url(@companies_industry_category), notice: "Companies industry category was successfully updated." }
        format.json { render :show, status: :ok, location: @companies_industry_category }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @companies_industry_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /companies_industry_categories/1 or /companies_industry_categories/1.json
  def destroy
    @companies_industry_category.destroy

    respond_to do |format|
      format.html { redirect_to companies_industry_categories_url, notice: "Companies industry category was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_companies_industry_category
      @companies_industry_category = CompaniesIndustryCategory.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def companies_industry_category_params
      params.require(:companies_industry_category).permit(:company_id, :industry_category_id)
    end
end
