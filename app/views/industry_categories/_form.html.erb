<%= form_with(model: industry_category) do |form| %>
  <% if industry_category.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(industry_category.errors.count, "error") %> prohibited this industry_category from being saved:</h2>

      <ul>
        <% industry_category.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :name, style: "display: block" %>
    <%= form.text_field :name, autofocus: true %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
