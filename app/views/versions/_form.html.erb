<%= form_with(model: version) do |form| %>
  <% if version.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(version.errors.count, "error") %> prohibited this version from being saved:</h2>

      <ul>
        <% version.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :version, style: "display: block" %>
    <%= form.text_field :version %>
  </div>

  <div>
    <%= form.label :remarks, style: "display: block" %>
    <%= form.text_area :remarks %>
  </div>

  <div>
    <%= form.label :deployed_at, style: "display: block" %>
    <%= form.date_field :deployed_at %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
