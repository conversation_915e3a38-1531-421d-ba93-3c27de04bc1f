<p style="color: green"><%= notice %></p>

<nav>
  <%= link_to t("companies"), companies_path %>
  |
  <%= link_to t("big_table"), big_table_companies_path %>
  |
  <%= link_to t("company_prefixes"), prefixes_companies_path %>
</nav>

<div class="form-header"><h1><%= t("search_update_company") %></h1></div>

<div class="filters-container">
  <div class="left-actions">
    <%= link_to t("create_company"), new_company_path, class: "button" %>
  </div>

  <div class="filters">
    <%= form_with url: companies_path, method: :get, local: true, class: "filter-form" do |form| %>
      <div class="filter-group">
        <%= form.label :query, t("search") + ":" %>
        <%= form.text_field :query, value: params[:query] %>
      </div>

      <div class="filter-group">
        <%= form.label :status, t("membership_status") + ":" %>
        <%= form.select :status,
            options_for_select(
              [[t('all_statuses'), ''], [t('active'), 'active'], [t('inactive'), 'inactive']],
              params[:status]
            ),
            {}, { class: 'status-select' }
        %>
      </div>

      <div class="filter-group">
        <%= form.label :sort_by, t("sort_by") + ":" %>
        <%= form.select :sort_by,
            options_for_select(
              [
                [t('ipgln_asc'), 'ipgln_asc'],
                [t('ipgln_desc'), 'ipgln_desc'],
                [t('name_asc'), 'name_asc'],
                [t('name_desc'), 'name_desc'],
                [t('expiry_date_asc'), 'expiry_asc'],
                [t('expiry_date_desc'), 'expiry_desc']
              ],
              params[:sort_by] || 'ipgln_asc'
            ),
            {}, { class: 'sort-select' }
        %>
      </div>

      <%= form.submit "Apply Filter", class: 'button' %>
      <%= link_to "Reset", companies_path, class: 'button button-secondary' %>
    <% end %>
  </div>
</div>

<p><%== pagy_info(@pagy) %></p>

<div id="companies">
  <table>
    <tr>
      <th><%= t("company_name") %></th>
      <th><%= t("company_ID_IPGLN") %></th>
      <th>產品數</th>
      <th><%= t("membership_status") %></th>
      <th><%= t("membership_expiry") %></th>
    </tr>
    <% @companies.each do |company| %>
      <tr>
        <td><%= link_to company.name, company %></td>
        <td><%= company.ipgln %></td>
        <td><%= company.products.count %></td>
        <td>
          <span
            class="<%= company.membership_due_at && company.membership_due_at >= Date.today ? 'status-active' : 'status-inactive' %>"
          >
            <%= company.membership_due_at && company.membership_due_at >= Date.today ? 'active' : 'inactive' %>
          </span>
        </td>
        <td>
          <%= company.membership_due_at&.strftime('%Y-%m-%d') || "N/A" %>

          <span
            class="<%= company.membership_due_at && company.membership_due_at >= Date.today ? 'days-remaining' : 'days-expired' %>"
          >
            <% if company.membership_due_at %>
              <% days_remaining = (company.membership_due_at.to_date - Date.current).to_i %>
              <% if days_remaining >= 0 %>
                (in <%= days_remaining %> days)
              <% else %>
                (<%= days_remaining.abs %> days expired)
              <% end %>
            <% end %>
          </span>
        </td>
      </tr>
    <% end %>
  </table>
</div>

<p><%== pagy_nav(@pagy) if @pagy.pages > 1 %></p>
