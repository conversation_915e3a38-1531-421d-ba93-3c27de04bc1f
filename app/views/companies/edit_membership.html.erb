<h1>Edit Membership for <%= @company.name %></h1>

<%= form_with(model: @company) do |form| %>
    <div>
        <%= form.label :company_nick_name %>
        <%= form.text_field :company_nick_name %>
    </div>
    <div>
        <%= form.label :member_num %>
        <%= form.text_field :member_num %>
    </div>
    <div>
        <%= form.label :join_member_at %>
        <%= form.date_field :join_member_at %>
    </div>
    <div>
        <%= form.label :identification_code %>
        <%= form.text_field :identification_code %>
    </div>
    <div>
        <%= form.label :password_pain %>
        <%= form.text_field :password_pain %>
    </div>
    <div>
        <%= form.label :contact_address %>
        <%= form.text_field :contact_address %>
    </div>
    <div>
        <%= form.label :certificate_issue_at %>
        <%= form.date_field :certificate_issue_at %>
    </div>
    <div>
        <%= form.label :certificate_valid_until %>
        <%= form.date_field :certificate_valid_until %>
    </div>
    <div>
        <%= form.label :contact_language %>
        <%= form.text_field :contact_language %>
    </div>


    <div class="field">
        <%= form.label :industry_category_ids, "Industry Categories" %>
        <%#
        This helper method is the magic bullet.
        - :industry_category_ids -> The attribute we're setting on the Company model.
        - IndustryCategory.all  -> The collection of objects to create checkboxes for.
        - :id                   -> The method to call on each category for the checkbox value.
        - :name                  -> The method to call on each category for the checkbox label.
        %>
        <%= form.collection_check_boxes :industry_category_ids, IndustryCategory.all, :id, :name do |b| %>
        <div class="collection-check-box">
            <%= b.check_box %>
            <%= b.label %>
        </div>
        <% end %>
    </div>


    <%= form.submit %>
<% end %>

<p><%= link_to "Cancel and back", company_path(@company) %></p>
