<p style="color: green"><%= notice %></p>

<h1>公司字首管理工具</h1>

<p>
  <%= link_to t("random_new"), random_new_prefix_companies_path, class: "button" %>
</p>

<div class="form-header">
    <h1><%= t("company_prefixes") %></h1>
</div>

<div id="companies">
  <table>
    <tr>
      <th><%= t("company_name") %></th>
      <th><%= t("company_prefix") %></th>
      <th>產品數</th>
      <th><%= t("company_prefix_max_count") %></th>
      <th><%= t("company_prefix_range") %></th>
    </tr>
    <% @companies.each do |company| %>
      <tr>
        <td><%= link_to company.name, company %></td>
        <td>
            <%= company.prefix %>
            <%= company.prefix_from_ipgln! if company.prefix.blank? %>
        </td>
        <td><%= company.products.count %></td>
        <td><%= number_with_delimiter company.prefix_max_products_allowed %></td>
        <td>
            <%= company.prefix_min_gtin %>
            –
            <%= company.prefix_max_gtin %>
        </td>
      </tr>
    <% end %>
  </table>
</div>

