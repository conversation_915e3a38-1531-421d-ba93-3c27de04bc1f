<div class="membership-status">
  <h3>Membership Status</h3>

  <div class="status-details">
    <% if company.membership_due_at.present? %>
      <% if company.membership_due_at.future? %>
        <div class="status-badge active">
          <i class="fas fa-check-circle"></i>
          <span>Active</span>
        </div>
        <p>Membership is active and will expire on <strong><%= company.membership_due_at.to_date %></strong>.</p>
      <% else %>
        <div class="status-badge expired">
          <i class="fas fa-exclamation-circle"></i>
          <span>Expired</span>
        </div>
        <p>Your membership expired on <strong><%= company.membership_due_at.to_date %></strong>.</p>
      <% end %>
    <% else %>
      <div class="status-badge inactive">
        <i class="fas fa-times-circle"></i>
        <span>No Active Membership</span>
      </div>
      <p>You don't have an active membership. Please contact support to renew.</p>
    <% end %>
  </div>

  <div class="actions">
    <% if company.membership_due_at.blank? || company.membership_due_at.past? %>
      <%= link_to "Renew Membership", new_company_membership_payment_history_path(company), class: "button primary" %>
    <% end %>
    <%= link_to "View Payment History", company_membership_payment_histories_path(company), class: "button" %>

    <%= link_to "Edit Membership", edit_membership_company_path(company), class: "button" %>
  </div>
</div>

<%# Add this to your CSS or stylesheet %>
<style>
.membership-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e9ecef;
}

.membership-status h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #343a40;
  font-size: 1.25rem;
}

.status-details {
  margin-bottom: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  margin-bottom: 1rem;
}

.status-badge i {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.expired {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.inactive {
  background-color: #e2e3e5;
  color: #383d41;
}

.membership-status p {
  margin: 0.5rem 0 0;
  color: #495057;
  line-height: 1.6;
}

.membership-status .actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

@media (max-width: 576px) {
  .membership-status .actions {
    flex-direction: column;
  }

  .membership-status .actions .button {
    width: 100%;
    text-align: center;
  }
}
</style>
