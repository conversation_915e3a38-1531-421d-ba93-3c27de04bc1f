<h1>公司字首管理工具: 抽新號</h1>

<nav>
  <%= link_to "抽新號", random_new_prefix_companies_path %>
  <%= link_to "管理幸運號碼", lucky_numbers_path %>
</nav>

<p>&nbsp;</p>

<p>
  <strong>
    <%= @prefix %>
  </strong>
</p>

<p>
  <strong><%= t("company_prefix_max_count") %>:</strong>
  <%= number_with_delimiter @temp_company.prefix_max_products_allowed %>
  <br>
  <%= @temp_company.prefix_min_gtin %>
  –
  <%= @temp_company.prefix_max_gtin %>
</p>


<div class="row">
  <div>
    <%= form_with url: random_new_prefix_companies_path, method: "get", local: true do |form| %>
      <%= form.label :length, "Length:" %>
      <%= form.number_field :length, value: @prefix_length, min: 4, max: 8 %>
      <%= form.submit "抽新號" %>
    <% end %>
  </div>

  <div>
    <%= form_with url: random_new_prefix_companies_path, method: "get", local: true do |form| %>
      <%= form.label :prefix, "Prefix:" %>
      <%= form.text_field :prefix, value: @prefix %>
      <%= form.submit "檢查是否有重複" %>
    <% end %>
  </div>

</div>

<p>&nbsp;</p>

<p>
  <%= link_to "列出全部", random_new_prefix_companies_path(list_all: "true", prefix: @prefix, length: @prefix_length), class: "button" %>
</p>

<div id="companies">
  <table>
    <tr>
      <th><%= t("company_name") %></th>
      <th><%= t("company_prefix") %></th>
      <th>產品數</th>
      <th><%= t("company_prefix_max_count") %></th>
      <th><%= t("company_prefix_range") %></th>
    </tr>
    <% @companies.each do |company| %>
      <tr>
        <td><%= link_to company.name, company %></td>
        <td>
            <%= company.prefix %>
            <%= company.prefix_from_ipgln! if company.prefix.blank? %>
        </td>
        <td><%= company.products.count %></td>
        <td><%= number_with_delimiter company.prefix_max_products_allowed %></td>
        <td>
            <small>
              <%= company.prefix_min_gtin %>
              –
              <%= company.prefix_max_gtin %>
            </small>
        </td>
      </tr>
    <% end %>
  </table>
</div>

<% if @companies.count == 0 %>
  <p>
      已檢查 <%= @total_prefixes %> 個前綴碼，沒有重複。
  </p>
<% end %>