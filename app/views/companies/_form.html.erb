<%= form_with(model: company) do |form| %>
  <% if company.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(company.errors.count, "error") %> prohibited this company from being saved:</h2>

      <ul>
        <% company.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <p>[*<%= t("is_mandatory_field") %>]</p>
  </div>

  <div class="form-subheader"><%= t("details") %></div>
  <div class="required">
    <%= form.label :ipgln, t("company_ID_IPGLN") %>
    <%= form.text_field :ipgln, required: true %>
  </div>

  <div>
    <%= form.label :prefix, t("company_prefix") %>
    <%= form.text_field :prefix %>
  </div>

  <%= form.fields_for :company_prefixes do |prefixes_form| %>
      <div class="row">
          <div>
            <%= prefixes_form.label :gcpkey, t("company_prefix") %>
            <%= prefixes_form.text_field :gcpkey %>
            <%= prefixes_form.hidden_field :id %>
          </div>

          <div>
            <%= prefixes_form.label :gcptype, t("type") %>
            <%= prefixes_form.text_field :gcptype %>
          </div>
      </div>
  <% end %>


  <div class="row">
    <div>
      <%= form.label :name_en, t("company_name") %>(EN)
      <%= form.text_field :name_en %>
    </div>
    <div>
      <%= form.label :name, t("company_name") %>(繁)
      <%= form.text_field :name %>
    </div>

    <div>
      <%= form.label :name_cn, t("company_name") %>(简)
      <%= form.text_field :name_cn %>
    </div>

  </div>

  <div>
    <%= form.label :email, t("email_address") %>
    <%= form.text_field :email %>
  </div>

  <div>
    <%= form.label :website_url, t("website_url") %>
    <%= form.text_field :website_url %>
  </div>

  <div>
    <%= form.label :telephone, t("telephone_number") %>
    <%= form.text_field :telephone %>
  </div>

  <div>
    <%= form.label :fax, t("fax_number") %>
    <%= form.text_field :fax %>
  </div>

  <div class="row">
    <div>
      <%= form.label :location_en, t("name_of_the_location") %>(EN)
      <%= form.text_field :location_en %>
    </div>
    <div>
      <%= form.label :location, t("name_of_the_location") %>(繁)
      <%= form.text_field :location %>
    </div>

    <div>
      <%= form.label :location_cn, t("name_of_the_location") %>(简)
      <%= form.text_field :location_cn %>
    </div>

  </div>

  <div class="row">
    <div>
      <%= form.label :floor_en, t("floor_and_block") %>(EN)
      <%= form.text_field :floor_en %>
    </div>
    <div>
      <%= form.label :floor, t("floor_and_block") %>(繁)
      <%= form.text_field :floor %>
    </div>
    <div>
      <%= form.label :floor_cn, t("floor_and_block") %>(简)
      <%= form.text_field :floor_cn %>
    </div>

  </div>

  <div class="row">
    <div>
      <%= form.label :street_en, t("building_and_street") %>(EN)
      <%= form.text_field :street_en %>
    </div>
    <div>
      <%= form.label :street, t("building_and_street") %>(繁)
      <%= form.text_field :street %>
    </div>
    <div>
      <%= form.label :street_cn, t("building_and_street") %>(简)
      <%= form.text_field :street_cn %>
    </div>

  </div>

  <div class="row">
    <div>
      <%= form.label :city_en, t("city") %>(EN)
      <%= form.text_field :city_en %>
    </div>
    <div>
      <%= form.label :city, t("city") %>(繁)
      <%= form.text_field :city %>
    </div>
    <div>
      <%= form.label :city_cn, t("city") %>(简)
      <%= form.text_field :city_cn %>
    </div>

  </div>

  <div>
    <%= form.label :country, t("country_area") %>
    <%= form.text_field :country %>
  </div>

  <div class="form-subheader"><%= t("person_in_charge") %></div>

  <div class="row">
    <div>
      <%= form.label :in_charge_person_name_en, t("name") %>(EN)
      <%= form.text_field :in_charge_person_name_en %>
    </div>
    <div>
      <%= form.label :in_charge_person_name, t("name") %>(繁)
      <%= form.text_field :in_charge_person_name %>
    </div>

    <div>
      <%= form.label :in_charge_person_name_cn, t("name") %>(简)
      <%= form.text_field :in_charge_person_name_cn %>
    </div>

  </div>

  <div>
    <%= form.label :in_charge_job_title, t("job_title") %>
    <%= form.text_field :in_charge_job_title %>
  </div>

  <div>
    <%= form.label :in_charge_email, t("email_address") %>
    <%= form.text_field :in_charge_email %>
  </div>

  <div>
    <%= form.label :in_charge_telephone, t("telephone_number") %>
    <%= form.text_field :in_charge_telephone %>
  </div>

  <div class="form-subheader"><%= t("contact_person") %></div>
  <div class="row">
    <div>
      <%= form.label :contact_person_name_en, t("name") %>(EN)
      <%= form.text_field :contact_person_name_en %>
    </div>
    <div>
      <%= form.label :contact_person_name, t("name") %>(繁)
      <%= form.text_field :contact_person_name %>
    </div>

    <div>
      <%= form.label :contact_person_name_cn, t("name") %>(简)
      <%= form.text_field :contact_person_name_cn %>
    </div>


  </div>

  <div>
    <%= form.label :contact_job_title, t("job_title") %>
    <%= form.text_field :contact_job_title %>
  </div>

  <div>
    <%= form.label :contact_email, t("email_address") %>
    <%= form.text_field :contact_email %>
  </div>

  <div>
    <%= form.label :contact_telephone, t("telephone_number") %>
    <%= form.text_field :contact_telephone %>
  </div>

  <div class="form-subheader"><%= t("administrator") %></div>

  <div class="row">
    <div>
      <%= form.label :admin_first_name_en, t("first_name") %>(EN)
      <%= form.text_field :admin_first_name_en %>
    </div>
    <div>
      <%= form.label :admin_first_name, t("first_name") %>(繁)
      <%= form.text_field :admin_first_name %>
    </div>
    <div>
      <%= form.label :admin_first_name_cn, t("first_name") %>(简)
      <%= form.text_field :admin_first_name_cn %>
    </div>
  </div>
  <div class="row">
    <div>
      <%= form.label :admin_last_name_en, t("last_name") %>(EN)
      <%= form.text_field :admin_last_name_en %>
    </div>
    <div>
      <%= form.label :admin_last_name, t("last_name") %>(繁)
      <%= form.text_field :admin_last_name %>
    </div>
    <div>
      <%= form.label :admin_last_name_cn, t("last_name") %>(简)
      <%= form.text_field :admin_last_name_cn %>
    </div>
  </div>

  <div>
    <%= form.label :admin_title, t("title") %>
    <%= form.text_field :admin_title %>
  </div>
  <div>
    <%= form.label :admin_gender, t("gender") %>
    <%= form.text_field :admin_gender %>
  </div>
  <div>
    <%= form.label :admin_job_title, t("job_title") %>
    <%= form.text_field :admin_job_title %>
  </div>
  <div>
    <%= form.label :admin_email, t("email_address") %>
    <%= form.text_field :admin_email %>
  </div>
  <div>
    <%= form.label :admin_telephone, t("telephone_number") %>
    <%= form.text_field :admin_telephone %>
  </div>
  <div>
    <%= form.label :admin_remark, t("remark") %>
    <%= form.text_area :admin_remark %>
  </div>
  <div>
    <%= form.check_box :is_suspended, class: "inline-element" %>
    <%= form.label :is_suspended, t("suspend_user") %>
  </div>

  <div class="form-action">
    <%= form.submit value: t("submit") %>
  </div>
<% end %>
