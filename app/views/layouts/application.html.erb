<!DOCTYPE html>
<html>
  <head>
    <title>Barcodeplus2024</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/foundation-sites@6.8.1/dist/css/foundation.min.css" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/foundation-sites@6.8.1/dist/js/foundation.min.js" crossorigin="anonymous"></script>
  </head>

  <body>
    <header>
      <div class="left">
        <a class="logo-link" href="/">
          <img src="https://barcodeplus.gs1mo.org/eid/theme/gs1hk/images/logo.png" alt="">
          My GS1 Codes
        </a>
      </div>

      <div class="right">
        <% if user_signed_in? %>
          <%= current_user.email %>
        <% end %>
        <%= link_to t('member_sign_in'), new_user_session_path, class: "button" %>


        <% if I18n.locale == :zh %>
          <%= link_to "简体中文", "?locale=cn" %> | <%= link_to "English", "?locale=en" %>
        <% elsif I18n.locale == :en %>
          <%= link_to "繁體中文", "?locale=zh"  %> | <%= link_to "简体中文", "?locale=cn" %>
        <% else %>
          <%= link_to "繁體中文", "?locale=zh"  %> | <%= link_to "English", "?locale=en" %>
        <% end %>
      </div>

    </header>
    <nav class="main">
      <ul>
        <% if user_signed_in? %>
          <li><a href="/"><%= t('home') %></a>
          <li><%= link_to t('product'), products_path %>
          <li><%= link_to t('company'), companies_path %>
          <li><%= link_to t('user'), users_path %>
        <% end %>
      </ul>
    </nav>
    <main>
      <%= yield %>
    </main>

    <footer>
      <%= link_to "Disclaimer", "https://barcodeplus.gs1mo.org/eid/disclaimer.html" %>
      |
      <%= link_to "Privacy and Security Policy", "https://barcodeplus.gs1mo.org/eid/policy.html" %>

      <p>
      © <%= Time.now.year %> GS1 Macao, China, All rights reserved.
      </p>

      <% if user_signed_in? %>
        <%= link_to "Industry Categories", industry_categories_path %>
      <% end %>

      <p>Build <%= Version.latest.version %></p>

      <% if user_signed_in? %>
        <p><%= link_to "Sign out", destroy_user_session_path, data: { "turbo-method": :delete } %></p>
      <% end %>
    </footer>

    <script>
      $(document).foundation();
    </script>
  </body>
</html>
