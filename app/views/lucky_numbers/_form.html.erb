<%= form_with(model: lucky_number) do |form| %>
  <% if lucky_number.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(lucky_number.errors.count, "error") %> prohibited this lucky_number from being saved:</h2>

      <ul>
        <% lucky_number.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :number, style: "display: block" %>
    <%= form.text_field :number, autofocus: true %>
  </div>

  <div>
    <%= form.label :is_available, style: "display: block" %>
    <%= form.check_box :is_available %>
  </div>

  <div>
    <%= form.label :rank, style: "display: block" %>
    <%= form.select :rank, ["Silver", "Gold", "Platinum", "Diamond"] %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
