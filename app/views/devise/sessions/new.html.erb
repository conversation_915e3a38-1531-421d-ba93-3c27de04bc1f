<h2><%= t("account_login") %></h2>

<div class="alert alert-danger">
  <%= flash[:alert] %>
</div>

<%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
  <div class="field">
    <%= f.label t('user_email') %>
    <%= f.email_field :email, autofocus: true, autocomplete: "email", required: true %>
  </div>

  <div class="field">
    <%= f.label t('user_password') %>
    <%= f.password_field :password, autocomplete: "current-password", required: true %>
  </div>

  <% if devise_mapping.rememberable? %>
    <div class="field">
      <%= f.check_box :remember_me, class: "inline-element" %>
      <%= f.label 'remember_me', t('remember_me') %>
    </div>
  <% end %>

  <div class="actions">
    <%= f.submit t("login") %>
  </div>
<% end %>

<%= render "devise/shared/links" %>
