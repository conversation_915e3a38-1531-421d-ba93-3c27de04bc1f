<div class="page-header">
  <h1>
    <%= link_to @membership_payment_history.company.name, @membership_payment_history.company %> &raquo;
    Payment Details
  </h1>

  <div class="actions">
    <%= link_to "Back to #{@membership_payment_history.company.name}", @membership_payment_history.company, class: "button" %>
    <%= link_to "View All Payments", company_membership_payment_histories_path(@membership_payment_history.company), class: "button" %>
    <%= link_to "Edit Payment", edit_membership_payment_history_path(@membership_payment_history), class: "button primary" %>
  </div>
</div>

<div class="card">
  <div class="card-header">
    <h2>Payment Details</h2>
  </div>

  <div class="card-body">
    <div class="attribute-row">
      <div class="attribute-label">Payment Date:</div>
      <div class="attribute-value"><%= @membership_payment_history.payment_date %></div>
    </div>

    <div class="attribute-row">
      <div class="attribute-label">Amount:</div>
      <div class="attribute-value"><%= number_to_currency(@membership_payment_history.amount) %></div>
    </div>

    <div class="attribute-row">
      <div class="attribute-label">Payment Method:</div>
      <div class="attribute-value"><%= @membership_payment_history.payment_method.titleize %></div>
    </div>

    <% if @membership_payment_history.transaction_id.present? %>
      <div class="attribute-row">
        <div class="attribute-label">Transaction/Reference #:</div>
        <div class="attribute-value"><%= @membership_payment_history.transaction_id %></div>
      </div>
    <% end %>

    <% if @membership_payment_history.notes.present? %>
      <div class="attribute-row">
        <div class="attribute-label">Notes:</div>
        <div class="attribute-value"><%= simple_format(@membership_payment_history.notes) %></div>
      </div>
    <% end %>

    <div class="attribute-row">
      <div class="attribute-label">Recorded On:</div>
      <div class="attribute-value">
        <%= @membership_payment_history.created_at.to_date %>
        <% if @membership_payment_history.updated_at != @membership_payment_history.created_at %>
          <br><small>Last updated: <%= @membership_payment_history.updated_at.to_date %></small>
        <% end %>
      </div>
    </div>
  </div>

  <div class="card-footer">
    <% if @membership_payment_history.payment_date.present? && @membership_payment_history.payment_date <= Date.current %>
      <div class="alert alert-success">
        This payment was successfully recorded on <%= @membership_payment_history.created_at.to_date %>.
        <% if @membership_payment_history.company.membership_due_at.present? %>
          <br>Membership is valid through <strong><%= @membership_payment_history.company.membership_due_at.to_date %></strong>.
        <% end %>
      </div>
    <% end %>

    <div class="actions">
      <%= link_to "Back to #{@membership_payment_history.company.name}", @membership_payment_history.company, class: "button secondary" %>
      <%= link_to "Edit Payment", edit_membership_payment_history_path(@membership_payment_history), class: "button primary" %>
      <%= link_to "Delete Payment", @membership_payment_history,
                  method: :delete,
                  class: "button danger",
                  data: { confirm: 'Are you sure you want to delete this payment record? This cannot be undone.' } %>
    </div>
  </div>
</div>
