<div class="form-container">
  <% if @company %>
    <div class="form-header">
      <h2>
        <%= link_to @company.name, @company %> &raquo;
        <%= membership_payment_history.persisted? ? "Edit Payment" : "Record New Payment" %>
      </h2>
    </div>
  <% end %>


  <%= form_with(model: [@company, membership_payment_history].compact) do |form| %>
    <% if membership_payment_history.errors.any? %>
      <div class="alert alert-danger">
        <h4><%= pluralize(membership_payment_history.errors.count, "error") %> prohibited this payment from being saved:</h4>
        <ul>
          <% membership_payment_history.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="form-group">
      <%= form.label :payment_date, "Payment Date *", class: "form-label" %>
      <%= form.date_field :payment_date, class: "form-control", required: true %>
    </div>

    <div class="form-group">
      <%= form.label :amount, "Amount *", class: "form-label" %>
      <div class="input-group">
        <span class="input-group-text">$</span>
        <%= form.number_field :amount, step: '0.01', min: 0, class: "form-control", required: true, placeholder: "0.00" %>
      </div>
    </div>


    <div class="form-group">
      <%= form.label :payment_method, "Payment Method *", class: "form-label" %>
      <%= form.select :payment_method,
          options_for_select(
            [
              ['Credit Card', 'credit_card'],
              ['Bank Transfer', 'bank_transfer'],
              ['Check', 'check'],
              ['Cash', 'cash'],
              ['Other', 'other']
            ],
            membership_payment_history.payment_method
          ),
          { include_blank: 'Select a payment method...' },
          { class: 'form-select', required: true } %>
    </div>

    <div class="form-group">
      <%= form.label :transaction_id, "Transaction/Reference #", class: "form-label" %>
      <%= form.text_field :transaction_id, class: "form-control", placeholder: "Optional reference number" %>
    </div>

    <div class="form-group">
      <%= form.label :notes, "Notes", class: "form-label" %>
      <%= form.text_area :notes, class: "form-control", rows: 3, placeholder: "Any additional notes about this payment" %>
    </div>

    <div class="form-actions">
      <%= link_to "Cancel", @company || membership_payment_history.company, class: "button secondary" %>
      <%= form.submit membership_payment_history.persisted? ? "Update Payment" : "Record Payment", class: "button primary" %>

      <% if membership_payment_history.persisted? %>
        <%= link_to "Delete", membership_payment_history,
                    method: :delete,
                    class: "button danger",
                    data: { confirm: 'Are you sure you want to delete this payment record? This cannot be undone.' } %>
      <% end %>
    </div>
  <% end %>
</div>
