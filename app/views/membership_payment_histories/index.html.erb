<p style="color: green"><%= notice %></p>

<div class="page-header">
  <h1>
    <% if @company %>
      <%= link_to @company.name, @company %> &raquo;
    <% end %>
    Membership Payment History
  </h1>

  <div class="actions">
    <% if @company %>
      <%= link_to "Back to Company", @company, class: "button" %>
      <%= link_to "Record New Payment", new_company_membership_payment_history_path(@company), class: "button primary" %>
    <% else %>
      <%= link_to "Back to Dashboard", root_path, class: "button" %>
    <% end %>
  </div>
</div>

<div class="table-responsive">
  <table class="data-table">
    <thead>
      <tr>
        <% unless @company %>
          <th>Company</th>
        <% end %>
        <th>Payment Date</th>
        <th>Amount</th>
        <th>Payment Method</th>
        <th>Transaction ID</th>
        <th>Notes</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% if @membership_payment_histories.any? %>
        <% @membership_payment_histories.each do |payment| %>
          <tr>
            <% unless @company %>
              <td><%= link_to payment.company.name, payment.company %></td>
            <% end %>
            <td><%= payment.payment_date %></td>
            <td><%= number_to_currency(payment.amount) %></td>
            <td><%= payment.payment_method %></td>
            <td><%= payment.transaction_id %></td>
            <td><%= truncate(payment.notes, length: 30) %></td>
            <td class="actions">
              <%= link_to "View", payment, class: "button small" %>
              <%= link_to "Edit", edit_membership_payment_history_path(payment), class: "button small" %>
            </td>
          </tr>
        <% end %>
      <% else %>
        <tr>
          <td colspan="<%= @company ? 6 : 7 %>" class="no-records">No payment records found.</td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<% unless @company %>
  <div class="pagination">
    <%= will_paginate @membership_payment_histories, renderer: WillPaginate::ActionView::BootstrapLinkRenderer %>
  </div>
<% end %>
