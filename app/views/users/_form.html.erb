<%= form_with(model: user) do |form| %>
  <% if user.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(user.errors.count, "error") %> prohibited this user from being saved:</h2>

      <ul>
        <% user.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <p>[*<%= t("is_mandatory_field") %>]</p>
  </div>

  <%= form.label :username, t("user_name") %>
  <%= form.text_field :username %>

  <div class="form-subheader"><%= t("details") %></div>

  <%= form.label :title, t("title") %>
  <%= form.select :title, ["Mr.", "Mrs.", "Ms.", "Miss.", "Dr."], { prompt: t('selects:') } %>

  <div class="row">
    <div>
      <%= form.label :first_name, t("first_name") %>
      <%= form.text_field :first_name %>
    </div>

    <div>
      <%= form.label :first_name_cn, t("first_name") %>
      <%= form.text_field :first_name_cn %>
    </div>

    <div>
      <%= form.label :first_name_en, t("first_name") %>
      <%= form.text_field :first_name_en %>
    </div>
  </div>

  <div class="row">
    <div>
      <%= form.label :last_name, t("last_name") %>
      <%= form.text_field :last_name %>
    </div>

    <div>
      <%= form.label :last_name_cn, t("last_name") %>
      <%= form.text_field :last_name_cn %>
    </div>

    <div>
      <%= form.label :last_name_en, t("last_name") %>
      <%= form.text_field :last_name_en %>
    </div>
  </div>

  <div>
    <%= form.label :gender, t("gender") %>
    <%= form.select :gender, [[t("male"), "male"], [t("female"), "female"]], { prompt: t('selects:') } %>
  </div>

  <div>
    <%= form.label :job_title, t("job_title") %>
    <%= form.text_field :job_title %>
  </div>

  <div>
    <%= form.label :email, t("email_address") %>
    <%= form.text_field :email %>
  </div>

  <div>
    <%= form.label :tel_num, t("telephone_number") %>
    <%= form.text_field :tel_num %>
  </div>

  <p><%= t("users_roles") %>:</p>

  <div>
    <%= form.check_box :is_gtin_management, class: "inline-element" %>
    <%= form.label :is_gtin_management, t("gtin_management") %>
  </div>

  <div>
    <%= form.check_box :is_system_admin, class: "inline-element" %>
    <%= form.label :is_system_admin, t("system_admin") %>
  </div>

  <div>
    <%= form.check_box :is_user_management, class: "inline-element" %>
    <%= form.label :is_user_management, t("user_management") %>
  </div>

  <div>
    <%= form.label :remark, t("remark") %>
    <%= form.text_area :remark, rows: 5 %>
  </div>


  <div class="form-action">
    <%= form.submit %>
  </div>

<% end %>