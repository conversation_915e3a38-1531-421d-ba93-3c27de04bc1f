<p style="color: green"><%= notice %></p>

<div class="form-header"><h1><%= t("search_update_user") %></h1></div>

<p><%= link_to t("create_user"), new_user_registration_path, class:"button" %></p>

<%= form_with url: "/users", method: "get", local: true do |form| %>
  <%= form.label :query, t("search") + ":" %>
  <%= form.text_field :query %>
  <%= form.submit t("search") %>
<% end %>

<p><%== pagy_info(@pagy) %></p>

<div id="users">
  <table>
    <tr>
      <th><%= t("company_name") %>
      <th><%= t("email_address") %></th>
      <th><%= t("first_name") %></th>
      <th><%= t("last_name") %></th>
    </tr>
    <% @users.each do |user| %>
      <tr>
        <td><%= user.company.name %></td>
        <td><%= link_to user.email, user %></td>
        <td><%= user.first_name %></td>
        <td><%= user.last_name %></td>
      </tr>
    <% end %>
  </table>
</div>

<p><%== pagy_nav(@pagy) if @pagy.pages > 1 %></p>
