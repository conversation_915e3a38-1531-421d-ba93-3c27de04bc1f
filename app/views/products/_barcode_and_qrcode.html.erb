
<%
  require 'barby'
  require 'barby/barcode/code_128'
  require 'barby/barcode/ean_13'
  require 'barby/barcode/bookland'
  require 'barby/outputter/png_outputter'

  ## barcode = Barby::EAN13.new(product.gtin[0...-1])
  # barcode = Barby::Code128B.new(product.gtin12)
  # png = Barby::PngOutputter.new(barcode).to_png(xdim:2)

  # image_name = SecureRandom.hex

  # IO.binwrite("tmp/#{image_name}.png", png.to_s)
  # image_tag "data:image/png;base64,#{Base64.encode64(File.read("tmp/#{image_name}.png"))}"
%>
<!-- <%=  %> -->

<figure class="barcode">
    <img alt='Barcode Generator'
       src='https://barcode.tec-it.com/barcode.ashx?data=<%= product.gtin12 %>&code=EAN13'/>
</figure>

<%
  require "rqrcode"
  qr_code = RQRCode::QRCode.new("https://id.gs1mo.org/01/#{product.gtin14}")
  qr_png = qr_code.as_png(
      bit_depth: 1,
      border_modules: 4,
      color_mode: ChunkyPNG::COLOR_GRAYSCALE,
      color: "black",
      file: nil,
      fill: "white",
      module_px_size: 6,
      resize_exactly_to: false,
      resize_gte_to: false,
      size: 512
    )

  # name the image
  image_name = SecureRandom.hex

  IO.binwrite("tmp/#{image_name}.png", qr_png.to_s)
%>

<figure class="qr-code">
  <%= image_tag "data:image/png;base64,#{Base64.encode64(File.read("tmp/#{image_name}.png"))}" %>
  <figcaption>
    <%= link_to "https://id.gs1mo.org/01/#{product.gtin14}", "https://id.gs1mo.org/01/#{product.gtin14}", target: "_blank" %>
  </figcaption>
</figure>

</figure>

<aside>
  Code generation powered by
  <a href='https://www.tec-it.com' title='Barcode Software by TEC-IT' target='_blank'>
    TEC-IT Barcode Generator
  </a>
</aside>

