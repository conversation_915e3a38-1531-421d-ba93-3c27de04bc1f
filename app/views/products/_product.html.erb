<div id="<%= dom_id product %>">
  <div class="half-half row">
    <div>
      <p>
        <strong><%= t("gtin") %>:</strong>
        <%= product.gtin %>
      </p>

      <p>
        <strong><%= t("trade_item_unit") %>:</strong>
        <%= product.trade_item_unit %>
      </p>

      <div class="">
        <p>
          <strong><%= t("product_name") %>:</strong>
        </p>
        <div class="multi-lang-row">
          <span class='language-icon zh'></span><%= product.name %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon cn'></span><%= product.name_cn %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon en'></span><%= product.name_en %>
        </div>


      </div>

      <div class="">
        <p>
          <strong><%= t("product_description") %>:</strong>
        </p>
        <div class="multi-lang-row">
          <span class='language-icon zh'></span><%= product.description %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon cn'></span><%= product.description_cn %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon en'></span><%= product.description_en %>
        </div>

      </div>

      <div class="">
        <p>
          <strong><%= t("product_brand_name") %>:</strong>
        </p>
        <div class="multi-lang-row">
          <span class='language-icon zh'></span><%= product.brand_name %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon cn'></span><%= product.brand_name_cn %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon en'></span><%= product.brand_name_en %>
        </div>

      </div>

      <div class="">
        <p>
          <strong><%= t("product_owner_name") %>:</strong>
        </p>
        <div class="multi-lang-row">
          <span class='language-icon zh'></span><%= product.brand_owner_name %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon cn'></span><%= product.brand_owner_name_cn %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon en'></span><%= product.brand_owner_name_en %>
        </div>

      </div>

      <div class="">

        <p>
          <strong><%= t("product_owner_name") %>:</strong>
        </p>
        <div class="multi-lang-row">
          <span class='language-icon zh'></span><%= product.brand_owner_name %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon cn'></span><%= product.brand_owner_name_cn %>
        </div>
        <div class="multi-lang-row">
          <span class='language-icon en'></span><%= product.brand_owner_name_en %>
        </div>

      </div>

      <p>
        <strong><%= t("product_country_of_origin") %>:</strong>
        <%= product.country_of_origin %>
      </p>

      <p>
        <strong><%= t("internal_product_code") %>:</strong>
        <%= product.internal_product_code %>
      </p>

      <p>
        <strong><%= t("product_cross_weight") %>:</strong>
        <%= product.cross_weight %>
      </p>

      <p>
        <strong><%= t("unit") %>:</strong>
        <%= product.cross_weight_unit %>
      </p>

      <p>
        <strong><%= t("product_net_content") %>:</strong>
        <%= product.net_content %>
      </p>

      <p>
        <strong><%= t("unit") %>:</strong>
        <%= product.net_content_unit %>
      </p>

      <p>
        <strong><%= t("release_the_product_data_to_public") %>:</strong>
        <%= product.is_public_released %>
      </p>
    </div>

    <div>
      <p class="product-cover-image">
        <% if product.cover_image.attached? %>
          <figure class="product-cover">
            <%= image_tag product.cover_image, class:"cover-image" %>
          </figure>
        <% else %>
          <p>No image available</p>
        <% end %>
      </p>

      <hr>
      <%= render "barcode_and_qrcode", product: product %>
    </div>
  </div>



</div>
