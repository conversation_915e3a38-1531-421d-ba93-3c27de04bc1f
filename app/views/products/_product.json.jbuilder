json.extract! product, :id, :gtin, :trade_item_unit, :name, :name_cn, :name_en, :description, :description_cn, :description_en, :brand_name, :brand_name_cn, :brand_name_en, :brand_owner_name, :brand_owner_name_cn, :brand_owner_name_en, :country_of_origin, :internal_product_code, :cross_weight, :cross_weight_unit, :net_content, :net_content_unit, :is_public_released, :created_at, :updated_at
json.url product_url(product, format: :json)
