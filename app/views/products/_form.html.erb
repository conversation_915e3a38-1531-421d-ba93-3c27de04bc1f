<%= form_with(model: product) do |form| %>
  <% if product.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(product.errors.count, "error") %> prohibited this product from being saved:</h2>

      <ul>
        <% product.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <p>[*<%= t("is_mandatory_field") %>]</p>
  </div>

  <%# <%= form.label :company_prefixes, t("company_prefix") %>
  <%# <%= form.collection_select :company_prefixes, current_user.company.company_prefixes, :id, :gcpkey %>

  <%= form.label :company_id, t("company") %>
  <%= form.collection_select :company_id, Company.all, :id, :name_with_prefix %>

  <div>
    <%= form.check_box :is_draft, class: "inline-element" %>
    <%= form.label :is_draft %>
  </div>

  <div>
    <%= form.label :gtin, t("gtin") %>
    <%= form.text_field :gtin %>

    <p>
      <small>對上一個 GTIN 碼是: <%= Product.last_gtin(product.company_id) || "N/A" %></small>
    </p>
  </div>

  <ul class="tabs" data-tabs id="collapsing-tabs">
    <li class="tabs-title is-active"><a href="#panel1" aria-selected="true"><%= t("main") %></a></li>
    <li class="tabs-title"><a href="#panel2"><%= t("useful_information") %></a></li>
    <li class="tabs-title"><a href="#panel3"><%= t("packaging") %></a></li>
    <li class="tabs-title"><a href="#panel4"><%= t("price") %></a></li>
    <li class="tabs-title"><a href="#panel5"><%= t("attachment") %></a></li>
  </ul>

  <div class="tabs-content" data-tabs-content="collapsing-tabs">
    <div class="tabs-panel is-active" id="panel1">

      <div class="form-subheader">1.1 <%= t("product_hierarchies") %></div>

      <div>
        <%= form.label :trade_item_unit, t("trade_item_unit") %>
        <%= form.select :trade_item_unit, ['Base Unit', 'Pack Unit', 'Case', 'Container', 'Pallet', 'Mixed Module'] %>
      </div>

      <div class="form-subheader">1.2 <%= t("description") %></div>

      <div class="row">
        <div>
          <%= form.label :name_en, t("product_name") %>(EN)
          <%= form.text_field :name_en %>
        </div>
        <div>
          <%= form.label :name, t("product_name") %>(繁)
          <%= form.text_field :name %>
        </div>
        <div>
          <%= form.label :name_cn, t("product_name") %>(简)
          <%= form.text_field :name_cn %>
        </div>
      </div>

      <div class="row">
        <div>
          <%= form.label :description_en, t("product_description") %>(EN)
          <%= form.text_area :description_en %>
        </div>
        <div>
          <%= form.label :description, t("product_description") %>(繁)
          <%= form.text_area :description %>
        </div>

        <div>
          <%= form.label :description_cn, t("product_description") %>(简)
          <%= form.text_area :description_cn %>
        </div>
      </div>

      <div class="row">
        <div>
          <%= form.label :brand_name_en, t("product_brand_name") %>(EN)
          <%= form.text_field :brand_name_en %>
        </div>
        <div>
          <%= form.label :brand_name, t("product_brand_name") %>(繁)
          <%= form.text_field :brand_name %>
        </div>

        <div>
          <%= form.label :brand_name_cn, t("product_brand_name") %>(简)
          <%= form.text_field :brand_name_cn %>
        </div>
      </div>

      <div class="row">
        <div>
          <%= form.label :brand_owner_name_en, t("product_owner_name") %>(EN)
          <%= form.text_field :brand_owner_name_en %>
        </div>
        <div>
          <%= form.label :brand_owner_name, t("product_owner_name") %>(繁)
          <%= form.text_field :brand_owner_name %>
        </div>

        <div>
          <%= form.label :brand_owner_name_cn, t("product_owner_name") %>(简)
          <%= form.text_field :brand_owner_name_cn %>
        </div>
      </div>

      <div>
        <%= form.label :country_of_origin, t("product_country_of_origin") %>
        <%= form.text_field :country_of_origin %>
      </div>

      <div>
        <%= form.label :internal_product_code, t("internal_product_code") %>
        <%= form.text_field :internal_product_code %>
      </div>

      <div>
        <%= form.label :cover_image %>
        <%= form.file_field :cover_image %>
      </div>

      <div>
        <%= form.label :images, t("product_images") %>
        <%= form.file_field :images, multiple: true %>

        <% if product.images.attached? %>
          <div>
            <% product.images.each do |image| %>
              <%= image_tag(image, style: "width: 100px; height: 100px") %>
            <% end %>
          </div>
        <% end %>
      </div>

      <div class="form-subheader">1.3 <%= t("weight") %></div>
      <div class="row">
        <div>
          <%= form.label :cross_weight, t("product_cross_weight") %>
          <%= form.text_field :cross_weight %>
        </div>

        <div>
          <%= form.label :cross_weight_unit, t("unit") %>
          <%= form.text_field :cross_weight_unit %>
        </div>
      </div>

      <div class="row">
        <div>
          <%= form.label :net_content, t("product_net_content") %>
          <%= form.text_field :net_content %>
        </div>

        <div>
          <%= form.label :net_content_unit, t("unit") %>
          <%= form.text_field :net_content_unit %>
        </div>
      </div>

      <div class="form-subheader">1.4 <%= t("dimension") %></div>

        <div class="row">
          <div>
            <%= form.label :unit_of_measure, t("unit_of_measure") %>
            <%= form.text_field :unit_of_measure %>
          </div>
        </div>

        <div class="row">
          <div>
            <%= form.label :depth, t("depth") %>
            <%= form.text_field :depth %>
          </div>

          <div>
            <%= form.label :height, t("height") %>
            <%= form.text_field :height %>
          </div>

        </div>

        <div class="row">
          <div>
            <%= form.label :width, t("width") %>
            <%= form.text_field :width %>
          </div>

          <div>
            <%= form.label :diameter, t("diameter") %>
            <%= form.text_field :diameter %>
          </div>
        </div>

      <div class="form-subheader">1.5 <%= t("profile") %></div>

      <div>
        <%= form.check_box :is_public_released, class: "inline-element" %>
        <%= form.label :is_public_released, t("release_the_product_data_to_public") %>
      </div>

    </div>

  <div class="tabs-panel" id="panel2">

    <div class="form-subheader">2.1 <%= t("useful_information") %></div>

    <div class="row">
      <div>
        <%= form.label :use_function_en, t("use_function") %>(EN)
        <%= form.text_area :use_function_en, rows: 5 %>
      </div>
      <div>
        <%= form.label :use_function, t("use_function") %>(繁)
        <%= form.text_area :use_function, rows: 5 %>
      </div>

      <div>
        <%= form.label :use_function_cn, t("use_function") %>(简)
        <%= form.text_area :use_function_cn, rows: 5 %>
      </div>
    </div>

    <div class="row">
      <div>
        <%= form.label :direction_in_usage_en, t("direction_in_usage") %>(EN)
        <%= form.text_area :direction_in_usage_en %>
      </div>
      <div>
        <%= form.label :direction_in_usage, t("direction_in_usage") %>(繁)
        <%= form.text_area :direction_in_usage %>
      </div>

      <div>
        <%= form.label :direction_in_usage_cn, t("direction_in_usage") %>(简)
        <%= form.text_area :direction_in_usage_cn %>
      </div>
    </div>

    <div class="row">
      <div>
        <%= form.label :caution_en, t("caution") %>(EN)
        <%= form.text_area :caution_en %>
      </div>
      <div>
        <%= form.label :caution, t("caution") %>(繁)
        <%= form.text_area :caution %>
      </div>

      <div>
        <%= form.label :caution_cn, t("caution") %>(简)
        <%= form.text_area :caution_cn %>
      </div>
    </div>

    <div class="row">
      <div>
        <%= form.label :storage_instruction_en, t("storage_instruction_description") %>
        <%= form.text_area :storage_instruction_en %>
      </div>
      <div>
        <%= form.label :storage_instruction, t("storage_instruction_description") %>
        <%= form.text_area :storage_instruction %>
      </div>

      <div>
        <%= form.label :storage_instruction_cn, t("storage_instruction_description") %>
        <%= form.text_area :storage_instruction_cn %>
      </div>
    </div>

    <div class="row">
      <div>
        <%= form.label :benefit_en, t("benefit") %>(EN)
        <%= form.text_area :benefit_en %>
      </div>
      <div>
        <%= form.label :benefit, t("benefit") %>(繁)
        <%= form.text_area :benefit %>
      </div>

      <div>
        <%= form.label :benefit_cn, t("benefit") %>(简)
        <%= form.text_area :benefit_cn %>
      </div>
    </div>

    <div class="row">
      <div>
        <%= form.label :remark_en, t("remark") %>(EN)
        <%= form.text_area :remark_en %>
      </div>
      <div>
        <%= form.label :remark, t("remark") %>(繁)
        <%= form.text_area :remark %>
      </div>

      <div>
        <%= form.label :remark_cn, t("remark") %>(简)
        <%= form.text_area :remark_cn %>
      </div>
    </div>

    <div class="form-subheader">2.2 <%= t("relevant_parties") %></div>

    <div>
      <%= form.check_box :is_brand_owner, class: "inline-element" %>
      <%= form.label :is_brand_owner, t("role_brand_owner") %>
    </div>

    <div>
      <%= form.check_box :is_manufacturer, class: "inline-element" %>
      <%= form.label :is_manufacturer, t("role_brand_manufacturer") %>
    </div>

    <div>
      <%= form.label :manufacturer_gln, t("manufacturers_GLN") %>
      <%= form.text_field :manufacturer_gln %>
    </div>
    <div>
      <%= form.label :manufacturer_name, t("manufacturers_name") %>
      <%= form.text_field :manufacturer_name %>
    </div>
    <div>
      <%= form.label :manufacturer_address, t("manufacturers_address") %>
      <%= form.text_field :manufacturer_address %>
    </div>
    <div>
      <%= form.label :manufacturer_telephone, t("manufacturers_telephone") %>
      <%= form.text_field :manufacturer_telephone %>
    </div>
    <div>
      <%= form.label :manufacturer_fax, t("manufacturers_fax") %>
      <%= form.text_field :manufacturer_fax %>
    </div>
    <div>
      <%= form.label :manufacturer_email, t("manufacturers_email") %>
      <%= form.text_field :manufacturer_email %>
    </div>
    <div>
      <%= form.label :manufacturer_website, t("manufacturers_website") %>
      <%= form.text_field :manufacturer_website %>
    </div>


    <div>
      <%= form.check_box :is_distributor, class: "inline-element" %>
      <%= form.label :is_distributor, t("role_in_distributor") %>
    </div>

    <div>
      <%= form.label :distributor_name, t("distributors_name") %>
      <%= form.text_field :distributor_name %>
    </div>
    <div>
      <%= form.label :distributor_address, t("distributors_address") %>
      <%= form.text_field :distributor_address %>
    </div>
    <div>
      <%= form.label :distributor_telephone, t("distributors_telephone") %>
      <%= form.text_field :distributor_telephone %>
    </div>
    <div>
      <%= form.label :distributor_fax, t("distributors_fax") %>
      <%= form.text_field :distributor_fax %>
    </div>
    <div>
      <%= form.label :distributor_email, t("distributors_email") %>
      <%= form.text_field :distributor_email %>
    </div>
    <div>
      <%= form.label :distributor_website, t("distributors_website") %>
      <%= form.text_field :distributor_website %>
    </div>

    <div>
      <%= form.check_box :is_retailer, class: "inline-element" %>
      <%= form.label :is_retailer, t("role_retailer") %>
    </div>

  </div>

  <div class="tabs-panel" id="panel3">

    <div class="form-subheader">3.1 <%= t("packaging") %></div>

      <div>
        <%= form.label :outer_packaging_type, t("product_outer_packaging_type") %>
        <%= form.text_field :outer_packaging_type %>
      </div>

      <div class="form-subheader">3.2 <%= t("for_carton_only") %></div>

      <div>
        <%= form.label :number_of_layers_per_carton, t("number_of_layers_per_carton") %>
        <%= form.text_field :number_of_layers_per_carton %>
      </div>
      <div>
        <%= form.label :number_of_trade_items_per_carton_layer, t("number_of_trade_items_per_carton_layer") %>
        <%= form.text_field :number_of_trade_items_per_carton_layer %>
      </div>

      <div class="form-subheader">3.3 <%= t("for_pallet_only") %></div>

      <div>
        <%= form.label :number_of_layers_per_pailet, t("number_of_layers_per_pallet") %>
        <%= form.text_field :number_of_layers_per_pailet %>
      </div>
      <div>
        <%= form.label :number_of_trade_items_per_pailet_layer, t("number_of_trade_items_per_pallet_layer") %>
        <%= form.text_field :number_of_trade_items_per_pailet_layer %>
      </div>
      <div>
        <%= form.label :total_number_of_trade_items_per_pailet, t("total_number_of_trade_items_per_pallet") %>
        <%= form.text_field :total_number_of_trade_items_per_pailet %>
      </div>

  </div>

  <div class="tabs-panel" id="panel4">

    <div class="form-subheader">4.1 <%= t("recommeded_consumer_sales_price") %></div>

    <div>
      <%= form.label :price, t("price") %>
      <%= form.text_field :price %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :price_currency, t("price_currency") %>
      <%= form.text_field :price_currency %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :basic_unit, t("basic_unit") %>
      <%= form.text_field :basic_unit %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :basic_unit_unit, t("unit_of_measure") %>
      <%= form.text_field :basic_unit_unit %> (<%= t("2_decimal_place") %>)
    </div>

    <div class="form-subheader">4.2 <%= t("other_price_discount_price") %></div>

    <div>
      <%= form.label :discount_price, t("price") %>
      <%= form.text_field :discount_price %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :discount_price_currency, t("price_currency") %>
      <%= form.text_field :discount_price_currency %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :discount_basic_unit, t("basic_unit") %>
      <%= form.text_field :discount_basic_unit %> (<%= t("2_decimal_place") %>)
    </div>
    <div>
      <%= form.label :discount_basic_unit_unit, t("unit_of_measure") %>
      <%= form.text_field :discount_basic_unit_unit %> (<%= t("2_decimal_place") %>)
    </div>

  </div>
</div>


  <div class="form-action">
    <%= form.submit %>
  </div>
<% end %>
