<%= form_with(model: companies_industry_category) do |form| %>
  <% if companies_industry_category.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(companies_industry_category.errors.count, "error") %> prohibited this companies_industry_category from being saved:</h2>

      <ul>
        <% companies_industry_category.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= form.label :company_id, style: "display: block" %>
    <%= form.text_field :company_id %>
  </div>

  <div>
    <%= form.label :industry_category_id, style: "display: block" %>
    <%= form.text_field :industry_category_id %>
  </div>

  <div>
    <%= form.submit %>
  </div>
<% end %>
