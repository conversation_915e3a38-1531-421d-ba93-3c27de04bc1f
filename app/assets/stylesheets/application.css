/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

*{box-sizing: border-box;}

img {max-width: 100%;}

/* Status pills */
.status-active,
.status-inactive {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  text-transform: capitalize;
  letter-spacing: 0.025em;
}

.status-active {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #34d399;
}

.status-inactive {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #f87171;
}

/* Days remaining/expired styles */
.days-remaining,
.days-expired {
  font-weight: 900;
}

.days-remaining {
  color: #065f46;
  /* background-color: #d1fae5; */
}

.days-expired {
  color: #991b1b;
  /* background-color: #fee2e2; */
}

/* Filter controls */
.filters-container {
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters {
  margin-top: 1rem;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.filter-group input[type="text"],
.filter-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  text-align: center;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #2563eb;
}

.button-secondary {
  background-color: #6b7280;
}

.button-secondary:hover {
  background-color: #4b5563;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
  }
}

body {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}
header {
    background: #f4f4f4;
    padding: 1em;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    row-gap: 1.5em;

}

header .right > * {
    margin: 0 .5em;
}

header img {
    width: 150px;
}

a.logo-link {
    text-decoration: none;
}

nav.main {
    border-bottom: 1px solid #B1B3B3;
}
nav.main ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
}
nav.main a {
    text-decoration: none;
    color: #333;
    padding: 1em;
    display: block;
    border-top: 2px solid #fff;
}
nav.main a:hover {
    border-top-color: #E16C43;
}

main {
    width: 85%;
    margin: 3em auto;
    flex-grow: 1;
}

h1 {
    font-size: 1.2em;
}

@media screen and (min-width: 760px) {
    .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 8px;
    }
}

@media screen and (max-width: 760px) {
    main {
        width: 100%;
    }

    header {
        flex-direction: column;
    }
}

label {
    display: block;
    font-weight: bold;
    margin-top: 1em;
}
.required label:before {
    content: "*";
    color: red;
    padding-right: .2em;
    /* font-size:1.5em; */
}
input,
textarea {
    width: 100%;
    display: block;
    border: 1px solid #ccc;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    padding: 1em;
}
input[required] {
    background: #FFFFE0;
}

.button,
input[type="submit"] {
    background: #E16C43;
    color: #fff;
    border: 0;
    padding: 0.8em 1.5em;
    margin-top: 1em;
    cursor: pointer;
    font-weight: bold;
    font-size: 1em;
    text-decoration: none;

    &.rounded {
      border-radius: 999px;
    }
}
input.inline-element {
    display: inline;
    width: auto;
}

.form-header,
.form-subheader,
.form-action {
    color: #fff;
    padding: .5em;
    margin-top: 1em;
    font-weight: bold;
}
.form-header {
    background: #0E2B68;
}
.form-subheader {
    background: #898B8D;
}

.form-action {
    background: #F4F4F4;
    overflow: auto;
}
.form-action .button,
.form-action input[type="submit"] {
    width: auto;
    float: right;
    padding: 1em 3em;
}


table {
    border-collapse: collapse;
    width: 100%;
}
th, td {
    border: 1px solid #999;
    padding: .5em;
}


.search-big-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;

    background: url("https://barcodeplus.gs1mo.org/eid/theme/gs1hk/images/barcodeplus-search-background-2.jpg") no-repeat center center;
}

.search-big-box form {
    background: rgba(255,255,255,0.5);
    -webkit-backdrop-filter: blur(1px) saturate(1.5);
    backdrop-filter: blur(1px) saturate(1.5);
    width: 80%;
    padding: 1em;
    border-radius: .5em;
}


footer {
    background: #F5F5F5;
    color: #666;
    text-align: center;
    padding: 1em;
}
footer a {
    color: #3F8BB9;
    text-decoration: none;
}