// Membership related styles

// Status indicators
.status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  
  &.active {
    background-color: #d4edda;
    color: #155724;
  }
  
  &.expired {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  &.inactive {
    background-color: #e2e3e5;
    color: #383d41;
  }
}

// Membership payment history table
.table-responsive {
  overflow-x: auto;
  margin: 1.5rem 0;
  
  .data-table {
    width: 100%;
    border-collapse: collapse;
    
    th, td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid #dee2e6;
    }
    
    th {
      background-color: #f8f9fa;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 0.75rem;
      letter-spacing: 0.05em;
      color: #6c757d;
    }
    
    tr:hover {
      background-color: #f8f9fa;
    }
    
    .actions {
      white-space: nowrap;
      
      .button {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  
  .no-records {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
    font-style: italic;
  }
}

// Form styles
.form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .form-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
    
    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      
      a {
        color: #007bff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  
  .form-group {
    margin-bottom: 1.5rem;
    
    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #495057;
    }
    
    .form-control, .form-select {
      width: 100%;
      padding: 0.5rem 0.75rem;
      font-size: 1rem;
      line-height: 1.5;
      color: #495057;
      background-color: #fff;
      background-clip: padding-box;
      border: 1px solid #ced4da;
      border-radius: 0.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      
      &:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
    
    textarea.form-control {
      min-height: 100px;
    }
    
    .input-group {
      display: flex;
      
      .input-group-text {
        display: flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        text-align: center;
        white-space: nowrap;
        background-color: #e9ecef;
        border: 1px solid #ced4da;
        border-radius: 0.25rem 0 0 0.25rem;
      }
      
      .form-control {
        position: relative;
        flex: 1 1 auto;
        width: 1%;
        margin-bottom: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: 2rem;
    
    .button {
      margin-left: 0.5rem;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

// Card styles for show view
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
  
  .card-header {
    padding: 1.25rem 1.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    
    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
    }
  }
  
  .card-body {
    padding: 1.5rem;
    
    .attribute-row {
      display: flex;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f1f1f1;
      
      &:last-child {
        border-bottom: none;
      }
      
      .attribute-label {
        width: 200px;
        font-weight: 600;
        color: #495057;
      }
      
      .attribute-value {
        flex: 1;
        color: #212529;
      }
    }
  }
  
  .card-footer {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    
    .alert {
      padding: 1rem;
      margin-bottom: 1.5rem;
      border: 1px solid transparent;
      border-radius: 0.25rem;
      
      &.alert-success {
        color: #0f5132;
        background-color: #d1e7dd;
        border-color: #badbcc;
      }
    }
    
    .actions {
      display: flex;
      justify-content: flex-end;
      
      .button {
        margin-left: 0.5rem;
        
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
}

// Page header styles
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
  
  h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 500;
    color: #343a40;
    
    a {
      color: #007bff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .actions {
    display: flex;
    gap: 0.5rem;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    .attribute-row {
      flex-direction: column;
      
      .attribute-label {
        width: 100%;
        margin-bottom: 0.25rem;
      }
    }
  }
  
  .form-actions, .card-footer .actions {
    flex-direction: column;
    
    .button {
      width: 100%;
      margin: 0.25rem 0 !important;
    }
  }
}
