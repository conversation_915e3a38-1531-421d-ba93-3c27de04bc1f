class MembershipPaymentHistory < ApplicationRecord
  belongs_to :company

  # Validations
  # validates :amount, presence: true, numericality: { greater_than: 0 }
  validates :payment_date, presence: true
  validates :payment_method, presence: true

  # Callbacks
  after_create :update_company_membership_due

  private

  def update_company_membership_due
    # Update the company's membership_due_at to 1 year from the payment date
    new_due_date = payment_date + 1.year
    company.update(membership_due_at: new_due_date)
  end
end
