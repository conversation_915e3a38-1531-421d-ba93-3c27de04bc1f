class Product < ApplicationRecord
    belongs_to :company

    has_one_attached :cover_image
    has_many_attached :images

    # validates :name, presence: true
    # validates :price, presence: true
    validates :company_id, presence: true

    def self.search(query)
      unless query.blank?
        where('name LIKE ? or name_cn LIKE ? or name_en LIKE ? or gtin LIKE ?', "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%")
      else
        all
      end
    end

    def self.randomly_picked(count=6)
      order("RAND()").limit(count)
    end

    def to_s
      return name if name.present?
      return name_cn if name_cn.present?
      name_en
    end

    def gtin14
      if self.gtin.length == 13
        "0#{self.gtin}"
      else
        self.gtin
      end
    end

    def gtin12
      self.gtin[0...-1]
    end

    def self.last_gtin(company_id)
      record = Product.where(company_id: company_id).order(gtin: :desc).first
      return nil if record.nil?
      record.gtin
    end

end
