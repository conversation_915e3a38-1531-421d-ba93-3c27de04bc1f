class Company < ApplicationRecord
  has_many :users
  has_many :products
  has_many :company_prefixes
  has_many :membership_payment_histories, dependent: :destroy

  has_and_belongs_to_many :industry_categories, join_table: "companies_industry_categories"


  accepts_nested_attributes_for :company_prefixes, allow_destroy: true

  def self.search(query)
    unless query.blank?
      where('name LIKE ? or name_cn LIKE ? or name_en LIKE ? or ipgln LIKE ?', "%#{query}%", "%#{query}%", "%#{query}%", "%#{query}%")
    else
      all
    end
  end

  def self.generate_random_prefix(length = 6)
    while true
      prefix = '958'
      length.times { prefix += rand(0..9).to_s }

      if length == 5
        prefix = '9588'
        (length-1).times { prefix += rand(0..9).to_s }
      end

      if length == 6
        prefix = '95888'
        (length-2).times { prefix += rand(0..9).to_s }
      end

      # At least not duplicated
      if Company.where(prefix: prefix).none? and CompanyPrefix.where(gcpkey: prefix).none?
        # Check if part of the prefix is not used
        if Company.where("prefix LIKE ?", "#{prefix}%").none? and CompanyPrefix.where("gcpkey LIKE ?", "#{prefix}%").none?
          # Check if the prefix is not used by lucky numbers
          if LuckyNumber.where(number: "958#{prefix}").none?
            break
          end
        end
      end
    end
    prefix
  end

  def name_with_prefix
    "#{name} (#{prefix})"
  end

  def prefix_from_ipgln
    # Get ipgln without last digit and remove all tailing zeros.
    return nil if ipgln.blank?
    ipgln[0...-1].sub(/0+$/, '')
  end

  def prefix_from_ipgln!
    return nil if ipgln.blank?
    self.prefix = prefix_from_ipgln
    save
    prefix
  end

  def prefix_max_products_allowed
    return 0 if prefix.blank?

    10**(14 - 1 - 1 - prefix.length)
  end

  def prefix_min_gtin
    return 0 if prefix.blank?

    prefix + '0' * (14 - 1 - 1 - prefix.length)
  end

  def prefix_max_gtin
    return 0 if prefix.blank?

    prefix + '9' * (14 - 1 - 1 - prefix.length)
  end


end
