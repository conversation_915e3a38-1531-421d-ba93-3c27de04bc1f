# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t "hello"
#
# In views, this is aliased to just `t`:
#
#     <%= t("hello") %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   "true": "foo"
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  edit: "Edit"
  back: "Back"
  home: "Home"
  company: "Company"
  product: "Product"
  user: "User"
  member_sign_in: "Member Sign In"
  search: "Search"
  account_login: "Account Login"
  user_email: "User Email:"
  user_password: "User Password:"
  create: "Create"
  password_confirmation: "Password Confirmation:"
  remember_me: "Remember me"
  login: "Login"
  forgot_password?: "Forgot Password?"
  create_company: "Create Company"
  is_mandatory_field: "is Mandatory Field"
  details: "Details"
  company_ID_IPGLN: "Company ID (IPGLN)"
  company_prefix: "Company Prefix"
  company_name: "Company Name"
  email_address: "Email Address"
  website_url: "Website URL"
  telephone_number: "Telephone Number"
  fax_number: "Fax Number"
  name_of_the_location: "Name of the Location"
  floor_and_block: "Flat/Room, Floor and Block"
  building_and_street: "Building and Street"
  city: "City"
  country_area: "Country/Area"
  person_in_charge: "Person in Charge"
  name: "Name"
  job_title: "Job Title"
  contact_person: "Contact Person"
  administrator: "Administrator"
  first_name: "First Name"
  last_name: "Last Name"
  title: "Title"
  gender: "Gender"
  remark: "Remark"
  suspend_user: "Suspend User"
  submit: "Submit"
  register_new_product: "Register New Product"
  gtin: "GTIN"
  main: "Main"
  useful_information: "Userful Information"
  packaging: "Packaging"
  price: "Price"
  attachment: "Attachment"
  product_Hierarchies: "Product Hierarchies"
  description: "Description"
  weight: "Weight"
  dimension: "Dimension"
  profile: "Profile"
  relevant_parties: "Relevant Parties"
  for_carton_only: "For Carton Only"
  for_pallet_only: "For Pallet Only"
  recommeded_consumer_sales_price: "Recommeded Consumer Sales Price"
  other_price_discount_price: "Other Price / Discount Price"
  attachment_upload: "Attachment Upload"
  trade_item_unit: "Trade item unit"
  product_name: "Product Name"
  product_description: "Product Description"
  product_brand_name: "Product Brand Name"
  product_owner_name: "Product Brand Owner's Name"
  product_country_of_origin: "Product Country of Origin"
  internal_product_code: "Internal Product Code"
  product_images: "Product Image(s)"
  product_cross_weight: "Product Cross Weight"
  product_net_content: "Product Net Content"
  product_register_date: "Register Date"
  product_last_change_date: "Last Change Date"
  unit: "Unit"
  unit_of_measure: "Unit of Measure"
  depth: "Depth"
  height: "Height"
  width: "Width"
  diameter: "Diameter"
  release_the_product_data_to_public: "Release the Product Data to Public?"
  use_function: "Use / Function"
  direction_in_usage: "Direction in Usage"
  caution: "caution"
  storage_instruction_description: "Storage Instruction Description"
  benefit: "Benefit"
  role_brand_owner: "Your role in the Supply Chain - Brand Owner?"
  role_brand_manufacturer: "Your role in the Supply Chain - Manufacturer?"
  yes: "Yes"
  no: "No"
  manufacturers_GLN: "Manufacturer's GLN"
  manufacturers_name: "Manufacturer's Name"
  manufacturers_address: "Manufacturer's Address"
  manufacturers_telephone: "Manufacturer's Telephone"
  manufacturers_fax: "Manufacturer's Fax"
  manufacturers_email: "Manufacturer's Email"
  manufacturers_website: "Manufacturer's Website"
  role_in_distributor: "Your role in the Supply Chain - Distributor?"
  distributors_name: "Distributor's Name"
  distributors_address: "Distributor's Address"
  distributors_telephone: "Distributor's Telephone"
  distributors_fax: "Distributor's Fax"
  distributors_email: "Distributor's Email"
  distributors_website: "Distributor's Website"
  role_retailer: "Your role in the Supply Chain - Retailer?"
  product_outer_packaging_type: "Product Outer Packaging Type"
  number_of_layers_per_carton: "Number of Layers Per Carton"
  number_of_trade_items_per_carton_layer: "Number of Trade Items Per Carton Layer"
  number_of_layers_per_pallet: "Number of Layers Per Pallet"
  number_of_trade_items_per_pallet_layer: "Number of Trade Items Per Pallet Layer"
  total_number_of_trade_items_per_pallet: "Total Number of Trade Items Per Pallet"
  price_currency: "Price currency"
  basic_unit: "Basic unit"
  2_decimal_place: "2 decimal place"
  register_new_product: "Register new product"
  back_to_product_page: "Back to product page"
  search_update_product: "Search/Update product"
  create_company: "Create company"
  search_update_company: "Search/Update company"
  edit_company: "Edit company"
  search_update_user: "Search/Update user"
  create_user: "Create user"
  company_prefix: "Comapny Prefix"
  company_prefix_max_count: "Prefix Max Count"
  company_prefix_range: "Prefix Range"
  type: "Type"
  user_name: "User Name"
  users_roles: "Users Roles"
  edit_user: "Edit User"
