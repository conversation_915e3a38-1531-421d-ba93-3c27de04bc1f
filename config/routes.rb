Rails.application.routes.draw do
  resources :lucky_numbers
  resources :companies_industry_categories
  resources :industry_categories
  resources :versions
  resources :products

  get '01/:gtin', to: 'products#show_gtin', as: 'gtin'

  resources :companies do
    resources :membership_payment_histories, only: [:index, :new, :create]

    get :prefixes, on: :collection
    get :random_new_prefix, on: :collection

    get :big_table, on: :collection

    get 'edit_membership', on: :member
  end

  # Keep the standalone resources for other actions if needed
  resources :membership_payment_histories, except: [:index, :new, :create]
  devise_for :users,  controllers: { sessions: 'users/sessions' }
  resources :users, except: [:new]
  # devise_for :admins
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  get 'products/search' => 'products#search'

  # Defines the root path route ("/")
  root "home#index"
end
