# Namespace for legacy database migration tasks
# These tasks handle the migration of data from the old BarcodePlus system
# to the new Rails application database structure
namespace :old_db do

  # List all tasks in the namespace
  task :list_tasks do
    Rake::Task.tasks.each do |task|
      if task.name.start_with?('old_db:')
        puts task.name
      end
    end
  end

  # ============================================================================
  # DRY RUN TASKS - READ-ONLY PREVIEW OF MIGRATION DATA
  # ============================================================================
  # These tasks connect to the legacy database and generate preview files
  # showing what data would be migrated, without making any changes to the
  # local database. Use these tasks to verify data and connections before
  # running the actual migration.

  desc "DRY RUN: Preview company migration data (read-only, no database changes)"
  task dry_run_companies: :environment do
    puts "=" * 80
    puts "DRY RUN: Company Migration Preview"
    puts "=" * 80
    puts "This task will:"
    puts "- Connect to legacy database (read-only)"
    puts "- Preview company data that would be migrated"
    puts "- Generate preview files in tmp/migration_preview/"
    puts "- Make NO changes to local database"
    puts "=" * 80

    require 'mysql2'
    require 'fileutils'

    # Create preview directory
    preview_dir = Rails.root.join('tmp', 'migration_preview')
    FileUtils.mkdir_p(preview_dir)

    begin
      # Connect to legacy database
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )
      puts "✓ Successfully connected to legacy database"

      # Preview files
      companies_file = preview_dir.join('companies_preview.txt')
      companies_csv = preview_dir.join('companies_preview.csv')

      File.open(companies_file, 'w') do |f|
        File.open(companies_csv, 'w') do |csv|
          f.puts "Company Migration Preview - #{Time.current}"
          f.puts "=" * 60
          f.puts

          # CSV header
          csv.puts "old_CMPYID,IPGLN,EMAIL,TELNUM,WEBSITEURL,name_zh_TW,name_zh_CN,name_en,location_zh_TW,location_zh_CN,location_en"

          # Preview base company data
          f.puts "STEP 1: Base Company Data from tbl_company"
          f.puts "-" * 40

          query = "SELECT * FROM tbl_company LIMIT 10"
          stmt = db.query(query)

          company_count = 0
          stmt.each do |row|
            company_count += 1
            f.puts "Company #{company_count}:"
            f.puts "  CMPYID: #{row['CMPYID']}"
            f.puts "  IPGLN: #{row['IPGLN']}"
            f.puts "  EMAIL: #{row['EMAIL']}"
            f.puts "  TEL: #{row['TEL']}"
            f.puts "  URL: #{row['URL']}"
            f.puts

            # Add to CSV (base data only for now)
            csv.puts "#{row['CMPYID']},#{row['IPGLN']},#{row['EMAIL']},#{row['TEL']},#{row['URL']},,,,,,,"
          end

          f.puts "Total companies in tbl_company: #{db.query('SELECT COUNT(*) as count FROM tbl_company').first['count']}"
          f.puts

          # Preview Traditional Chinese translations
          f.puts "STEP 2: Traditional Chinese Translations (zh_TW)"
          f.puts "-" * 40

          query = "SELECT * FROM tbl_company_ml WHERE LANGID = 'zh_TW' LIMIT 10"
          stmt = db.query(query)

          zh_tw_count = 0
          stmt.each do |row|
            zh_tw_count += 1
            f.puts "zh_TW Translation #{zh_tw_count}:"
            f.puts "  CMPYID: #{row['CMPYID']}"
            f.puts "  Company Name: #{row['CMPYNAME_ML']}"
            f.puts "  Location: #{row['ADDR1_ML']}"
            f.puts "  Floor: #{row['ADDR2_ML']}"
            f.puts "  Street: #{row['ADDR3_ML']}"
            f.puts "  City: #{row['CITY_ML']}"
            f.puts
          end

          f.puts "Total zh_TW translations: #{db.query("SELECT COUNT(*) as count FROM tbl_company_ml WHERE LANGID = 'zh_TW'").first['count']}"
          f.puts

          # Preview Simplified Chinese translations
          f.puts "STEP 3: Simplified Chinese Translations (zh_CN)"
          f.puts "-" * 40

          zh_cn_count = db.query("SELECT COUNT(*) as count FROM tbl_company_ml WHERE LANGID = 'zh_CN'").first['count']
          f.puts "Total zh_CN translations: #{zh_cn_count}"
          f.puts

          # Preview English translations
          f.puts "STEP 4: English Translations (en)"
          f.puts "-" * 40

          en_count = db.query("SELECT COUNT(*) as count FROM tbl_company_ml WHERE LANGID = 'en'").first['count']
          f.puts "Total English translations: #{en_count}"
          f.puts

          # Summary
          f.puts "MIGRATION SUMMARY"
          f.puts "=" * 40
          f.puts "Base companies: #{db.query('SELECT COUNT(*) as count FROM tbl_company').first['count']}"
          f.puts "zh_TW translations: #{db.query("SELECT COUNT(*) as count FROM tbl_company_ml WHERE LANGID = 'zh_TW'").first['count']}"
          f.puts "zh_CN translations: #{zh_cn_count}"
          f.puts "English translations: #{en_count}"
          f.puts
          f.puts "Existing companies in local DB: #{Company.count}"
          f.puts "Companies with old_CMPYID: #{Company.where.not(old_CMPYID: nil).count}"
        end
      end

      puts "✓ Preview files generated:"
      puts "  - #{companies_file}"
      puts "  - #{companies_csv}"
      puts "✓ No changes made to local database"

    rescue Mysql2::Error => e
      puts "✗ Database connection error: #{e.message}"
      puts "Please check connection settings and network connectivity."
    ensure
      db&.close
      puts "✓ Database connection closed"
    end
  end

  desc "DRY RUN: Preview product migration data (read-only, no database changes)"
  task dry_run_products: :environment do
    puts "=" * 80
    puts "DRY RUN: Product Migration Preview"
    puts "=" * 80
    puts "This task will:"
    puts "- Connect to legacy database (read-only)"
    puts "- Preview product data that would be migrated"
    puts "- Generate preview files in tmp/migration_preview/"
    puts "- Make NO changes to local database"
    puts "=" * 80

    require 'mysql2'
    require 'fileutils'

    # Create preview directory
    preview_dir = Rails.root.join('tmp', 'migration_preview')
    FileUtils.mkdir_p(preview_dir)

    begin
      # Connect to legacy database
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )
      puts "✓ Successfully connected to legacy database"

      # Preview files
      products_file = preview_dir.join('products_preview.txt')
      products_csv = preview_dir.join('products_preview.csv')

      File.open(products_file, 'w') do |f|
        File.open(products_csv, 'w') do |csv|
          f.puts "Product Migration Preview - #{Time.current}"
          f.puts "=" * 60
          f.puts

          # CSV header
          csv.puts "old_PDID,GTIN,IPGLN,company_exists,PDGROSWGT,PDWGTUOM,PDNETCONT,PDNETCONTUOM,PDCNTYOFORGN,name_zh_TW,name_zh_CN,name_en"

          # Preview base product data
          f.puts "STEP 1: Base Product Data from tbl_pd"
          f.puts "-" * 40

          query = "SELECT * FROM tbl_pd LIMIT 10"
          stmt = db.query(query)

          product_count = 0
          stmt.each do |row|
            product_count += 1
            company_exists = Company.exists?(ipgln: row['IPGLN'])

            f.puts "Product #{product_count}:"
            f.puts "  PDID: #{row['PDID']}"
            f.puts "  GTIN: #{row['GTIN']}"
            f.puts "  IPGLN: #{row['IPGLN']} #{company_exists ? '(✓ Company exists)' : '(✗ Company missing)'}"
            f.puts "  Gross Weight: #{row['PDGROSWGT']} #{row['PDWGTUOM']}"
            f.puts "  Net Content: #{row['PDNETCONT']} #{row['PDNETCONTUOM']}"
            f.puts "  Country: #{row['PDCNTYOFORGN']}"
            f.puts "  Public: #{row['NONPUBIND'] != '1' ? 'Yes' : 'No'}"
            f.puts

            # Add to CSV
            csv.puts "#{row['PDID']},#{row['GTIN']},#{row['IPGLN']},#{company_exists},#{row['PDGROSWGT']},#{row['PDWGTUOM']},#{row['PDNETCONT']},#{row['PDNETCONTUOM']},#{row['PDCNTYOFORGN']},,,"
          end

          total_products = db.query('SELECT COUNT(*) as count FROM tbl_pd').first['count']
          f.puts "Total products in tbl_pd: #{total_products}"
          f.puts

          # Check company linkage
          f.puts "COMPANY LINKAGE ANALYSIS"
          f.puts "-" * 40

          # Get unique IPGLNs from products
          ipgln_query = "SELECT DISTINCT IPGLN FROM tbl_pd WHERE IPGLN IS NOT NULL"
          ipgln_stmt = db.query(ipgln_query)

          total_ipglns = 0
          existing_companies = 0
          missing_companies = []

          ipgln_stmt.each do |row|
            total_ipglns += 1
            if Company.exists?(ipgln: row['IPGLN'])
              existing_companies += 1
            else
              missing_companies << row['IPGLN']
            end
          end

          f.puts "Unique IPGLNs in products: #{total_ipglns}"
          f.puts "Companies exist in local DB: #{existing_companies}"
          f.puts "Missing companies: #{missing_companies.length}"
          if missing_companies.any?
            f.puts "Missing IPGLNs: #{missing_companies.first(5).join(', ')}#{missing_companies.length > 5 ? '...' : ''}"
          end
          f.puts

          # Preview multilingual data
          f.puts "STEP 2-4: Multilingual Product Data from tbl_pd_ml"
          f.puts "-" * 40

          ['zh_TW', 'zh_CN', 'en'].each do |lang|
            count = db.query("SELECT COUNT(*) as count FROM tbl_pd_ml WHERE LANGID = '#{lang}'").first['count']
            f.puts "#{lang} translations: #{count}"
          end
          f.puts

          # Sample multilingual data
          f.puts "Sample zh_TW Product Translations:"
          f.puts "-" * 30

          ml_query = "SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_TW' LIMIT 5"
          ml_stmt = db.query(ml_query)

          ml_stmt.each_with_index do |row, index|
            f.puts "#{index + 1}. PDID: #{row['PDID']}"
            f.puts "   Name: #{row['PDNAME_ML']}"
            f.puts "   Brand: #{row['PDBRNDNAME_ML']}"
            f.puts "   Description: #{row['PDDESC_ML']&.truncate(100)}"
            f.puts
          end

          # Summary
          f.puts "MIGRATION SUMMARY"
          f.puts "=" * 40
          f.puts "Total products to migrate: #{total_products}"
          percent = total_ipglns > 0 ? (existing_companies * 100 / total_ipglns) : 0
          f.puts "Products with valid company links: #{percent}% (#{existing_companies}/#{total_ipglns} unique IPGLNs)"
          f.puts "Existing products in local DB: #{Product.count}"
          f.puts "Products with old_PDID: #{Product.where.not(old_PDID: nil).count}"
        end
      end

      puts "✓ Preview files generated:"
      puts "  - #{products_file}"
      puts "  - #{products_csv}"
      puts "✓ No changes made to local database"

    rescue Mysql2::Error => e
      puts "✗ Database connection error: #{e.message}"
      puts "Please check connection settings and network connectivity."
    ensure
      db&.close
      puts "✓ Database connection closed"
    end
  end

  desc "DRY RUN: Preview user migration data (read-only, no database changes)"
  task dry_run_users: :environment do
    puts "=" * 80
    puts "DRY RUN: User Migration Preview"
    puts "=" * 80
    puts "This task will:"
    puts "- Connect to legacy database (read-only)"
    puts "- Preview user data that would be migrated"
    puts "- Generate preview files in tmp/migration_preview/"
    puts "- Make NO changes to local database"
    puts "=" * 80

    require 'mysql2'
    require 'fileutils'

    # Create preview directory
    preview_dir = Rails.root.join('tmp', 'migration_preview')
    FileUtils.mkdir_p(preview_dir)

    begin
      # Connect to legacy database
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )
      puts "✓ Successfully connected to legacy database"

      # Preview files
      users_file = preview_dir.join('users_preview.txt')
      users_csv = preview_dir.join('users_preview.csv')

      File.open(users_file, 'w') do |f|
        File.open(users_csv, 'w') do |csv|
          f.puts "User Migration Preview - #{Time.current}"
          f.puts "=" * 60
          f.puts

          # CSV header
          csv.puts "old_USERID,CMPYID,company_exists,USERNAME,EMAIL,TITLE,GENDER,JOBTITLE,TELNUM,ISADMIN,USERTYPE,USERSTATUS"

          # Preview user data
          f.puts "USER DATA from tbl_user"
          f.puts "-" * 40

          query = "SELECT * FROM tbl_user LIMIT 10"
          stmt = db.query(query)

          user_count = 0
          stmt.each do |row|
            user_count += 1
            company_exists = Company.exists?(old_CMPYID: row['CMPYID'])

            f.puts "User #{user_count}:"
            f.puts "  USERID: #{row['USERID']}"
            f.puts "  CMPYID: #{row['CMPYID']} #{company_exists ? '(✓ Company exists)' : '(✗ Company missing)'}"
            f.puts "  USERNAME: #{row['USERNAME']}"
            f.puts "  EMAIL: #{row['EMAIL']}"
            f.puts "  TITLE: #{row['TITLE']}"
            f.puts "  GENDER: #{row['GENDER']}"
            f.puts "  JOB TITLE: #{row['JOBTITLE']}"
            f.puts "  TEL: #{row['TELNUM']}"
            f.puts "  MOBILE: #{row['MOBILE']}"
            f.puts "  IS ADMIN: #{row['ISADMIN'] == '1' ? 'Yes' : 'No'}"
            f.puts "  USER TYPE: #{row['USERTYPE']}"
            f.puts "  STATUS: #{row['USERSTATUS']}"
            f.puts "  LAST LOGIN: #{row['LASTLOGINTIME']}"
            f.puts

            # Add to CSV
            csv.puts "#{row['USERID']},#{row['CMPYID']},#{company_exists},#{row['USERNAME']},#{row['EMAIL']},#{row['TITLE']},#{row['GENDER']},#{row['JOBTITLE']},#{row['TELNUM']},#{row['ISADMIN']},#{row['USERTYPE']},#{row['USERSTATUS']}"
          end

          total_users = db.query('SELECT COUNT(*) as count FROM tbl_user').first['count']
          f.puts "Total users in tbl_user: #{total_users}"
          f.puts

          # Check company linkage
          f.puts "COMPANY LINKAGE ANALYSIS"
          f.puts "-" * 40

          # Get unique CMPYIDs from users
          cmpyid_query = "SELECT DISTINCT CMPYID FROM tbl_user WHERE CMPYID IS NOT NULL"
          cmpyid_stmt = db.query(cmpyid_query)

          total_cmpyids = 0
          existing_companies = 0
          missing_companies = []

          cmpyid_stmt.each do |row|
            total_cmpyids += 1
            if Company.exists?(old_CMPYID: row['CMPYID'])
              existing_companies += 1
            else
              missing_companies << row['CMPYID']
            end
          end

          f.puts "Unique CMPYIDs in users: #{total_cmpyids}"
          f.puts "Companies exist in local DB: #{existing_companies}"
          f.puts "Missing companies: #{missing_companies.length}"
          if missing_companies.any?
            f.puts "Missing CMPYIDs: #{missing_companies.first(5).join(', ')}#{missing_companies.length > 5 ? '...' : ''}"
          end
          f.puts

          # User statistics
          f.puts "USER STATISTICS"
          f.puts "-" * 40

          admin_count = db.query("SELECT COUNT(*) as count FROM tbl_user WHERE ISADMIN = '1'").first['count']
          active_count = db.query("SELECT COUNT(*) as count FROM tbl_user WHERE USERSTATUS = 1").first['count']

          f.puts "Total users: #{total_users}"
          f.puts "Admin users: #{admin_count}"
          f.puts "Active users: #{active_count}"
          f.puts "Users with email: #{db.query("SELECT COUNT(*) as count FROM tbl_user WHERE EMAIL IS NOT NULL AND EMAIL != ''").first['count']}"
          f.puts

          # Check for multilingual user data
          f.puts "MULTILINGUAL USER DATA"
          f.puts "-" * 40

          ml_user_count = db.query("SELECT COUNT(*) as count FROM tbl_user_ml").first['count']
          f.puts "Records in tbl_user_ml: #{ml_user_count}"

          if ml_user_count > 0
            f.puts "Sample multilingual user data:"
            ml_query = "SELECT * FROM tbl_user_ml LIMIT 3"
            ml_stmt = db.query(ml_query)

            ml_stmt.each_with_index do |row, index|
              f.puts "  #{index + 1}. USERID: #{row['USERID']}, LANG: #{row['LANGID']}"
              f.puts "     First Name: #{row['FIRSTNAME_ML']}"
              f.puts "     Last Name: #{row['LASTNAME_ML']}"
            end
          end
          f.puts

          # Summary
          f.puts "MIGRATION SUMMARY"
          f.puts "=" * 40
          f.puts "Total users to migrate: #{total_users}"
          percent = total_cmpyids > 0 ? (existing_companies * 100 / total_cmpyids) : 0
          f.puts "Users with valid company links: #{percent}% (#{existing_companies}/#{total_cmpyids} unique CMPYIDs)"
          f.puts "Admin users: #{admin_count}"
          f.puts "Active users: #{active_count}"
          f.puts "Existing users in local DB: #{User.count}"
          f.puts "Users with old_USERID: #{User.where.not(old_USERID: nil).count}"
        end
      end

      puts "✓ Preview files generated:"
      puts "  - #{users_file}"
      puts "  - #{users_csv}"
      puts "✓ No changes made to local database"

    rescue Mysql2::Error => e
      puts "✗ Database connection error: #{e.message}"
      puts "Please check connection settings and network connectivity."
    ensure
      db&.close
      puts "✓ Database connection closed"
    end
  end

  desc "DRY RUN: Preview all migration data (companies, products, users)"
  task dry_run_all: :environment do
    puts "=" * 80
    puts "DRY RUN: Complete Migration Preview"
    puts "=" * 80
    puts "Running all dry run tasks..."
    puts

    Rake::Task['old_db:dry_run_companies'].invoke
    puts
    Rake::Task['old_db:dry_run_products'].invoke
    puts
    Rake::Task['old_db:dry_run_users'].invoke

    puts
    puts "=" * 80
    puts "All dry run tasks completed!"
    puts "Check tmp/migration_preview/ for detailed preview files"
    puts "=" * 80
  end

  desc "DRY RUN: Preview product images data (read-only, no downloads)"
  task dry_run_images: :environment do
    puts "=" * 80
    puts "DRY RUN: Product Images Preview"
    puts "=" * 80
    puts "This task will:"
    puts "- Connect to legacy database (read-only)"
    puts "- Preview product image data that would be downloaded"
    puts "- Generate preview files in tmp/migration_preview/"
    puts "- Make NO downloads or file changes"
    puts "=" * 80

    require 'mysql2'
    require 'fileutils'
    require 'uri'
    require 'net/http'

    # Create preview directory
    preview_dir = Rails.root.join('tmp', 'migration_preview')
    FileUtils.mkdir_p(preview_dir)

    begin
      # Connect to legacy database
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )
      puts "✓ Successfully connected to legacy database"

      # Preview files
      images_file = preview_dir.join('images_preview.txt')
      images_csv = preview_dir.join('images_preview.csv')

      File.open(images_file, 'w') do |f|
        File.open(images_csv, 'w') do |csv|
          f.puts "Product Images Preview - #{Time.current}"
          f.puts "=" * 60
          f.puts

          # CSV header
          csv.puts "PDID,SEQNO,IMGFILE,MODDATE,ACTIVE,source_url,target_filename,product_exists"

          # Preview image data
          f.puts "PRODUCT IMAGES from tbl_pdimage"
          f.puts "-" * 40

          query = "SELECT * FROM tbl_pdimage WHERE ACTIVE = '1' ORDER BY PDID, SEQNO LIMIT 20"
          stmt = db.query(query)

          image_count = 0
          stmt.each do |row|
            image_count += 1

            # Extract GTIN from IMGFILE path (first part before /)
            gtin = row['IMGFILE'].split('/').first
            filename = File.basename(row['IMGFILE'])

            # Construct source URL
            source_url = "https://barcodeplus.gs1mo.org/eid/resource/libx/dfile/gtin:#{row['IMGFILE']}"

            # Construct target filename: {pdid}-{gtin}-{filename}
            target_filename = "#{row['PDID']}-#{gtin}-#{filename}"

            # Check if product exists
            product_exists = Product.exists?(old_PDID: row['PDID'])

            f.puts "Image #{image_count}:"
            f.puts "  PDID: #{row['PDID']} #{product_exists ? '(✓ Product exists)' : '(✗ Product missing)'}"
            f.puts "  SEQNO: #{row['SEQNO']}"
            f.puts "  IMGFILE: #{row['IMGFILE']}"
            f.puts "  GTIN: #{gtin}"
            f.puts "  Filename: #{filename}"
            f.puts "  Modified: #{row['MODDATE']}"
            f.puts "  Active: #{row['ACTIVE']}"
            f.puts "  Source URL: #{source_url}"
            f.puts "  Target filename: #{target_filename}"
            f.puts

            # Add to CSV
            csv.puts "#{row['PDID']},#{row['SEQNO']},#{row['IMGFILE']},#{row['MODDATE']},#{row['ACTIVE']},#{source_url},#{target_filename},#{product_exists}"
          end

          # Statistics
          total_images = db.query('SELECT COUNT(*) as count FROM tbl_pdimage').first['count']
          active_images = db.query("SELECT COUNT(*) as count FROM tbl_pdimage WHERE ACTIVE = '1'").first['count']
          unique_products = db.query('SELECT COUNT(DISTINCT PDID) as count FROM tbl_pdimage WHERE ACTIVE = \'1\'').first['count']

          f.puts "IMAGE STATISTICS"
          f.puts "=" * 40
          f.puts "Total image records: #{total_images}"
          f.puts "Active images: #{active_images}"
          f.puts "Unique products with images: #{unique_products}"
          f.puts

          # Check product linkage
          f.puts "PRODUCT LINKAGE ANALYSIS"
          f.puts "-" * 40

          # Get unique PDIDs from images
          pdid_query = "SELECT DISTINCT PDID FROM tbl_pdimage WHERE ACTIVE = '1'"
          pdid_stmt = db.query(pdid_query)

          total_pdids = 0
          existing_products = 0
          missing_products = []

          pdid_stmt.each do |row|
            total_pdids += 1
            if Product.exists?(old_PDID: row['PDID'])
              existing_products += 1
            else
              missing_products << row['PDID']
            end
          end

          f.puts "Unique PDIDs with images: #{total_pdids}"
          f.puts "Products exist in local DB: #{existing_products}"
          f.puts "Missing products: #{missing_products.length}"
          if missing_products.any?
            f.puts "Missing PDIDs: #{missing_products.first(5).join(', ')}#{missing_products.length > 5 ? '...' : ''}"
          end
          f.puts

          # File type analysis
          f.puts "FILE TYPE ANALYSIS"
          f.puts "-" * 40

          file_types = {}
          file_query = "SELECT IMGFILE FROM tbl_pdimage WHERE ACTIVE = '1'"
          file_stmt = db.query(file_query)

          file_stmt.each do |row|
            ext = File.extname(row['IMGFILE']).downcase
            file_types[ext] = (file_types[ext] || 0) + 1
          end

          file_types.each do |ext, count|
            f.puts "#{ext.empty? ? '(no extension)' : ext}: #{count} files"
          end
          f.puts

          # Summary
          f.puts "DOWNLOAD SUMMARY"
          f.puts "=" * 40
          f.puts "Total images to download: #{active_images}"
          f.puts "Products with valid links: #{existing_products * 100 / total_pdids}% (#{existing_products}/#{total_pdids})"
          f.puts "Estimated download size: Unknown (would need to check each URL)"
          f.puts "Target directory: storage/legacy_images/"
        end
      end

      puts "✓ Preview files generated:"
      puts "  - #{images_file}"
      puts "  - #{images_csv}"
      puts "✓ No downloads performed"

    rescue Mysql2::Error => e
      puts "✗ Database connection error: #{e.message}"
      puts "Please check connection settings and network connectivity."
    ensure
      db&.close
      puts "✓ Database connection closed"
    end
  end

  desc "Download all product images from legacy database"
  task download_images: :environment do
    puts "=" * 80
    puts "DOWNLOADING PRODUCT IMAGES"
    puts "=" * 80
    puts "This task will:"
    puts "- Connect to legacy database"
    puts "- Download all active product images"
    puts "- Save images to storage/legacy_images/"
    puts "- Generate download report"
    puts "=" * 80

    require 'mysql2'
    require 'fileutils'
    require 'uri'
    require 'net/http'
    require 'openssl'

    # Create directories
    images_dir = Rails.root.join('storage', 'legacy_images')
    reports_dir = Rails.root.join('tmp', 'migration_reports')
    FileUtils.mkdir_p(images_dir)
    FileUtils.mkdir_p(reports_dir)

    # Report files
    report_file = reports_dir.join("image_download_report_#{Time.current.strftime('%Y%m%d_%H%M%S')}.txt")
    success_csv = reports_dir.join("successful_downloads_#{Time.current.strftime('%Y%m%d_%H%M%S')}.csv")
    failed_csv = reports_dir.join("failed_downloads_#{Time.current.strftime('%Y%m%d_%H%M%S')}.csv")

    begin
      # Connect to legacy database
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )
      puts "✓ Successfully connected to legacy database"

      # Initialize counters and arrays
      total_images = 0
      downloaded_count = 0
      failed_count = 0
      skipped_count = 0
      total_size = 0
      successful_downloads = []
      failed_downloads = []

      File.open(report_file, 'w') do |report|
        File.open(success_csv, 'w') do |success_csv_file|
          File.open(failed_csv, 'w') do |failed_csv_file|

            report.puts "Product Images Download Report - #{Time.current}"
            report.puts "=" * 60
            report.puts "Start time: #{Time.current}"
            report.puts "Target directory: #{images_dir}"
            report.puts

            # CSV headers
            success_csv_file.puts "PDID,SEQNO,IMGFILE,source_url,target_filename,file_size,download_time"
            failed_csv_file.puts "PDID,SEQNO,IMGFILE,source_url,target_filename,error_message"

            # Get all active images
            puts "Fetching image records from database..."
            query = "SELECT * FROM tbl_pdimage WHERE ACTIVE = '1' ORDER BY PDID, SEQNO"
            stmt = db.query(query)

            total_images = stmt.count
            puts "Found #{total_images} active images to download"
            report.puts "Total active images found: #{total_images}"
            report.puts

            # Process each image
            stmt.each_with_index do |row, index|
              begin
                # Extract GTIN from IMGFILE path (first part before /)
                gtin = row['IMGFILE'].split('/').first
                filename = File.basename(row['IMGFILE'])

                # Construct source URL
                source_url = "https://barcodeplus.gs1mo.org/eid/resource/libx/dfile/gtin:#{row['IMGFILE']}"

                # Construct target filename: {pdid}-{gtin}-{filename}
                target_filename = "#{row['PDID']}-#{gtin}-#{filename}"
                target_path = images_dir.join(target_filename)

                # Skip if file already exists
                if File.exist?(target_path)
                  skipped_count += 1
                  puts "Skipping #{target_filename} (already exists)" if (index + 1) % 10 == 0
                  next
                end

                # Download the image
                puts "Downloading #{index + 1}/#{total_images}: #{target_filename}" if (index + 1) % 10 == 0 || index < 10

                uri = URI(source_url)
                download_start = Time.current

                Net::HTTP.start(uri.host, uri.port, use_ssl: uri.scheme == 'https',
                               verify_mode: OpenSSL::SSL::VERIFY_NONE) do |http|
                  request = Net::HTTP::Get.new(uri)

                  http.request(request) do |response|
                    if response.code == '200'
                      file_size = 0
                      File.open(target_path, 'wb') do |file|
                        response.read_body do |chunk|
                          file.write(chunk)
                          file_size += chunk.size
                        end
                      end

                      download_time = Time.current - download_start
                      downloaded_count += 1
                      total_size += file_size

                      successful_downloads << {
                        pdid: row['PDID'],
                        seqno: row['SEQNO'],
                        imgfile: row['IMGFILE'],
                        source_url: source_url,
                        target_filename: target_filename,
                        file_size: file_size,
                        download_time: download_time
                      }

                      # Log to CSV
                      success_csv_file.puts "#{row['PDID']},#{row['SEQNO']},#{row['IMGFILE']},#{source_url},#{target_filename},#{file_size},#{download_time}"

                    else
                      raise "HTTP #{response.code}: #{response.message}"
                    end
                  end
                end

              rescue => e
                failed_count += 1
                error_message = e.message.gsub(',', ';') # Escape commas for CSV

                failed_downloads << {
                  pdid: row['PDID'],
                  seqno: row['SEQNO'],
                  imgfile: row['IMGFILE'],
                  source_url: source_url,
                  target_filename: target_filename,
                  error: error_message
                }

                # Log to CSV
                failed_csv_file.puts "#{row['PDID']},#{row['SEQNO']},#{row['IMGFILE']},#{source_url},#{target_filename},#{error_message}"

                puts "Failed to download #{target_filename}: #{error_message}"
                report.puts "FAILED: #{target_filename} - #{error_message}"
              end

              # Progress update every 50 downloads
              if (index + 1) % 50 == 0
                puts "Progress: #{index + 1}/#{total_images} processed (#{downloaded_count} downloaded, #{failed_count} failed, #{skipped_count} skipped)"
              end
            end

            # Final report
            report.puts
            report.puts "DOWNLOAD SUMMARY"
            report.puts "=" * 40
            report.puts "End time: #{Time.current}"
            report.puts "Total images processed: #{total_images}"
            report.puts "Successfully downloaded: #{downloaded_count}"
            report.puts "Failed downloads: #{failed_count}"
            report.puts "Skipped (already exist): #{skipped_count}"
            report.puts "Total downloaded size: #{(total_size / 1024.0 / 1024.0).round(2)} MB"
            report.puts

            if failed_downloads.any?
              report.puts "FAILED DOWNLOADS (first 10):"
              report.puts "-" * 30
              failed_downloads.first(10).each do |failed|
                report.puts "#{failed[:target_filename]}: #{failed[:error]}"
              end
              report.puts "... see #{failed_csv} for complete list" if failed_downloads.length > 10
            end
          end
        end
      end

      puts
      puts "=" * 80
      puts "DOWNLOAD COMPLETED!"
      puts "=" * 80
      puts "Total images processed: #{total_images}"
      puts "Successfully downloaded: #{downloaded_count}"
      puts "Failed downloads: #{failed_count}"
      puts "Skipped (already exist): #{skipped_count}"
      puts "Total downloaded size: #{(total_size / 1024.0 / 1024.0).round(2)} MB"
      puts
      puts "Files saved to: #{images_dir}"
      puts "Reports generated:"
      puts "  - #{report_file}"
      puts "  - #{success_csv}"
      puts "  - #{failed_csv}"

    rescue Mysql2::Error => e
      puts "✗ Database connection error: #{e.message}"
      puts "Please check connection settings and network connectivity."
    ensure
      db&.close
      puts "✓ Database connection closed"
    end
  end

  # ============================================================================
  # ACTUAL MIGRATION TASKS
  # ============================================================================
  desc "Migrate company data from legacy database to new schema"

  # Task: old_db_migrate_companies
  # Purpose: Migrates company records from the legacy tbl_company and tbl_company_ml tables
  # to the new companies table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_company: Contains main company information
  # - tbl_company_ml: Contains multilingual company information (zh_TW, en, zh_CN)
  #
  # Migration Strategy:
  # 1. First migrate base company data from tbl_company
  # 2. Then overlay Traditional Chinese (zh_TW) translations
  # 3. Finally overlay English (en) translations
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_CMPYID
  task old_db_migrate_companies: :environment do
    puts "Starting company migration from legacy database..."
    puts "This task migrates company data from tbl_company and tbl_company_ml tables"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # STEP 1: Migrate base company data from tbl_company
      # This table contains the primary company information without translations
      puts "Step 1: Migrating base company data from tbl_company..."
      query = <<~SQL
        SELECT *
        FROM tbl_company
      SQL
      stmt = db.query(query)

      # Legacy tbl_company table structure (for reference)
      # Sample record showing the structure of data we're migrating from:
      #{"CMPYID"=>"fee88f869754360e07f4bb600b152e44",        # Unique company identifier
      # "ACTYPE"=>0,                                         # Account type
      # "MEMBTYPE"=>nil,                                     # Membership type
      # "SUBMITDATE"=>2019-07-12 00:00:00 +0800,           # Date company was submitted/registered
      # "SUSPENDDATE"=>nil,                                  # Date company was suspended (if any)
      # "STATUS"=>"1",                                       # Company status (1=active)
      # "CRTSYS"=>"2",                                       # Creation system identifier
      # "TEL"=>"********",                                   # Primary telephone number
      # "FAX"=>nil,                                          # Fax number
      # "EMAIL"=>"<EMAIL>",                          # Primary email address
      # "URL"=>nil,                                          # Company website URL
      # "ISPUBLICSEARCHABLE"=>nil,                          # Whether company is publicly searchable
      # "IPGLN"=>"*************",                           # International Product Group Location Number
      # "EXPIRYDATE"=>nil,                                   # Membership expiry date
      # "AUTOGEN"=>"Y",                                      # Whether IPGLN was auto-generated
      # "COUNTRY"=>nil,                                      # Country code
      # "INCHARGEPERSONTITLE"=>nil,                         # Title of person in charge
      # "INCHARGE_EMAIL"=>nil,                              # Email of person in charge
      # "INCHARGE_PHONE"=>nil,                              # Phone of person in charge
      # "INCHARGE_FAX"=>nil,                                # Fax of person in charge
      # "CONTACTTITLE"=>nil,                                # Contact person title
      # "CONTACT_EMAIL"=>"<EMAIL>",                  # Contact person email
      # "CONTACT_PHONE"=>"********",                        # Contact person phone
      # "CONTACT_FAX"=>nil,                                 # Contact person fax
      # "REMARK"=>nil,                                       # Additional remarks
      # "LASTMDFUSER"=>"IMPORT",                            # Last user who modified the record
      # "LASTMDFDATE"=>2019-07-12 00:00:00 +0800,          # Last modification date
      # "VERSION"=>0,                                        # Record version for optimistic locking
      # "NONPUBIND"=>"1",                                   # Non-public indicator
      # "CCSEXPIRY"=>nil,                                   # CCS expiry date
      # "CRMNO"=>nil}                                       # CRM number

      # Process each company record from the legacy tbl_company table
      company_count = 0
      stmt.each do |row|
          # Find existing company by legacy ID or create new one
          # This prevents duplicate entries during re-runs of the migration
          c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

          # Map basic company information from legacy fields
          c.ipgln = row['IPGLN']                    # International Product Group Location Number
          # Note: PREFIX, NAME fields don't exist in legacy tbl_company - they come from tbl_company_ml
          # c.prefix = row['PREFIX']                # Will be set from multilingual table
          # c.name = row['NAME']                    # Will be set from multilingual table
          # c.name_cn = row['NAME_CN']              # Will be set from multilingual table
          # c.name_en = row['NAME_EN']              # Will be set from multilingual table

          # Contact information available in base table
          c.email = row['EMAIL']                    # Primary company email
          c.website_url = row['URL']                # Company website URL
          c.telephone = row['TEL']                  # Primary telephone number
          c.fax = row['FAX']                        # Fax number

          # Address fields don't exist in base table - they come from tbl_company_ml
          # c.location = row['LOCATION']            # Will be set from multilingual table
          # c.location_cn = row['LOCATION_CN']      # Will be set from multilingual table
          # c.location_en = row['LOCATION_EN']      # Will be set from multilingual table
          # c.floor = row['FLOOR']                  # Will be set from multilingual table
          # c.floor_cn = row['FLOOR_CN']            # Will be set from multilingual table
          # c.floor_en = row['FLOOR_EN']            # Will be set from multilingual table
          # c.street = row['STREET']                # Will be set from multilingual table
          # c.street_cn = row['STREET_CN']          # Will be set from multilingual table
          # c.street_en = row['STREET_EN']          # Will be set from multilingual table
          # c.city = row['CITY']                    # Will be set from multilingual table
          # c.city_cn = row['CITY_CN']              # Will be set from multilingual table
          # c.city_en = row['CITY_EN']              # Will be set from multilingual table
          # c.country = row['COUNTRY']              # Available in base table but often null

          # Person in charge information (limited in base table)
          # c.in_charge_person_name = row['IN_CHARGE_PERSON_NAME']     # Will be set from multilingual table
          # c.in_charge_person_name_cn = row['IN_CHARGE_PERSON_NAME_CN'] # Will be set from multilingual table
          # c.in_charge_person_name_en = row['IN_CHARGE_PERSON_NAME_EN'] # Will be set from multilingual table
          # c.in_charge_job_title = row['IN_CHARGE_JOB_TITLE']         # Will be set from multilingual table
          c.in_charge_email = row['INCHARGE_EMAIL']                   # Person in charge email
          c.in_charge_telephone = row['INCHARGE_PHONE']               # Person in charge phone

          # Contact person information (limited in base table)
          # c.contact_person_name = row['CONTACT_PERSON_NAME']         # Will be set from multilingual table
          # c.contact_person_name_cn = row['CONTACT_PERSON_NAME_CN']   # Will be set from multilingual table
          # c.contact_person_name_en = row['CONTACT_PERSON_NAME_EN']   # Will be set from multilingual table
          # c.contact_job_title = row['CONTACT_JOB_TITLE']             # Will be set from multilingual table
          c.contact_email = row['CONTACT_EMAIL']                      # Contact person email
          c.contact_telephone = row['CONTACT_PHONE']                  # Contact person phone
          # Administrator information (not available in base table)
          # c.admin_first_name = row['ADMIN_FIRST_NAME']               # Will be set separately if needed
          # c.admin_first_name_cn = row['ADMIN_FIRST_NAME_CN']         # Will be set separately if needed
          # c.admin_first_name_en = row['ADMIN_FIRST_NAME_EN']         # Will be set separately if needed
          # c.admin_last_name = row['ADMIN_LAST_NAME']                 # Will be set separately if needed
          # c.admin_last_name_cn = row['ADMIN_LAST_NAME_CN']           # Will be set separately if needed
          # c.admin_last_name_en = row['ADMIN_LAST_NAME_EN']           # Will be set separately if needed
          # c.admin_title = row['ADMIN_TITLE']                         # Will be set separately if needed

          # Store legacy tracking information for audit trail
          c.old_CMPYID = row['CMPYID']              # Original company ID from legacy system
          c.old_LASTMDFUSER = row['LASTMDFUSER']    # Last modification user in legacy system
          c.old_LASTMDFDATE = row['LASTMDFDATE']    # Last modification date in legacy system

          # Save the company record
          if c.save
            company_count += 1
            print "." if company_count % 10 == 0  # Progress indicator
          else
            puts "Failed to save company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
          end
      end
      puts "\nStep 1 completed: #{company_count} companies migrated from tbl_company"

      # STEP 2: Overlay Traditional Chinese (zh_TW) translations from tbl_company_ml
      # This table contains multilingual company information
      puts "\nStep 2: Overlaying Traditional Chinese translations from tbl_company_ml..."

      # Legacy tbl_company_ml table structure (for reference)
      # Sample record showing Traditional Chinese data:
      #{"CMPYID"=>"4fc58c542fa62e4c0ca5018e600216b4",        # Links to company in tbl_company
      # "LANGID"=>"zh_TW",                                   # Language identifier (zh_TW, en, zh_CN)
      # "CMPYNAME_ML"=>"澳门润澳生物科技有限公司",              # Company name in specified language
      # "ADDR1_ML"=>"南方大厦",                              # Address line 1 (building/location)
      # "ADDR2_ML"=>"4楼A座",                               # Address line 2 (floor)
      # "ADDR3_ML"=>"友谊大马路1017号",                       # Address line 3 (street)
      # "CITY_ML"=>"澳门",                                   # City name in specified language
      # "INCHARGEPERSONNAME_ML"=>"石翰中",                   # Person in charge name in specified language
      # "CONTACTNAME_ML"=>"石翰中",                          # Contact person name in specified language
      # "ID"=>1398}                                          # Unique record ID for this translation

      # Query for Traditional Chinese translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_TW'
      SQL
      stmt = db.query(query)

      # Process Traditional Chinese translations
      zh_tw_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set Traditional Chinese (default) fields
        # These become the primary fields since zh_TW is the default language
        c.name = row['CMPYNAME_ML']                         # Company name in Traditional Chinese
        c.location = row['ADDR1_ML']                        # Building/location name in Traditional Chinese
        c.floor = row['ADDR2_ML']                           # Floor information in Traditional Chinese
        c.street = row['ADDR3_ML']                          # Street address in Traditional Chinese
        c.city = row['CITY_ML']                             # City name in Traditional Chinese
        c.in_charge_person_name = row['INCHARGEPERSONNAME_ML']  # Person in charge name in Traditional Chinese
        c.contact_person_name = row['CONTACTNAME_ML']       # Contact person name in Traditional Chinese

        # Save the updated company record
        if c.save
          zh_tw_count += 1
          print "." if zh_tw_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_TW data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 2 completed: #{zh_tw_count} companies updated with Traditional Chinese translations"

      # STEP 3: Overlay Simplified Chinese (zh_CN) translations from tbl_company_ml
      puts "\nStep 3: Overlaying Simplified Chinese translations from tbl_company_ml..."

      # Query for Simplified Chinese translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)

      # Process Simplified Chinese translations
      zh_cn_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set Simplified Chinese specific fields
        c.name_cn = row['CMPYNAME_ML']                         # Company name in Simplified Chinese
        c.location_cn = row['ADDR1_ML']                        # Building/location name in Simplified Chinese
        c.floor_cn = row['ADDR2_ML']                           # Floor information in Simplified Chinese
        c.street_cn = row['ADDR3_ML']                          # Street address in Simplified Chinese
        c.city_cn = row['CITY_ML']                             # City name in Simplified Chinese
        c.in_charge_person_name_cn = row['INCHARGEPERSONNAME_ML']  # Person in charge name in Simplified Chinese
        c.contact_person_name_cn = row['CONTACTNAME_ML']       # Contact person name in Simplified Chinese

        # Save the updated company record
        if c.save
          zh_cn_count += 1
          print "." if zh_cn_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_CN data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 3 completed: #{zh_cn_count} companies updated with Simplified Chinese translations"

      # STEP 4: Overlay English (en) translations from tbl_company_ml
      puts "\nStep 4: Overlaying English translations from tbl_company_ml..."

      # Query for English translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)

      # Process English translations
      en_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set English specific fields
        c.name_en = row['CMPYNAME_ML']                         # Company name in English
        c.location_en = row['ADDR1_ML']                        # Building/location name in English
        c.floor_en = row['ADDR2_ML']                           # Floor information in English
        c.street_en = row['ADDR3_ML']                          # Street address in English
        c.city_en = row['CITY_ML']                             # City name in English
        c.in_charge_person_name_en = row['INCHARGEPERSONNAME_ML']  # Person in charge name in English
        c.contact_person_name_en = row['CONTACTNAME_ML']       # Contact person name in English

        # Save the updated company record
        if c.save
          en_count += 1
          print "." if en_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save English data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 4 completed: #{en_count} companies updated with English translations"
      puts "\nCompany migration completed successfully!"
      puts "Total companies processed: #{company_count}"
      puts "- Traditional Chinese translations: #{zh_tw_count}"
      puts "- Simplified Chinese translations: #{zh_cn_count}"
      puts "- English translations: #{en_count}"

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end

  desc "Migrate product data from legacy database to new schema"

  # Task: old_db_migrate_products
  # Purpose: Migrates product records from the legacy tbl_pd and tbl_pd_ml tables
  # to the new products table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_pd: Contains main product information
  # - tbl_pd_ml: Contains multilingual product information (zh_TW, en, zh_CN)
  #
  # Migration Strategy:
  # 1. First migrate base product data from tbl_pd
  # 2. Then overlay multilingual translations from tbl_pd_ml
  # 3. Link products to companies using IPGLN matching
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_PDID
  task old_db_migrate_products: :environment do
    puts "Starting product migration from legacy database..."
    puts "This task migrates product data from tbl_pd and tbl_pd_ml tables"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # First, examine the structure of the legacy tbl_pd table
      puts "Examining legacy tbl_pd table structure..."
      query = <<~SQL
        SELECT *
        FROM tbl_pd
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Legacy tbl_pd table structure (for reference)
      # Sample record showing the structure of product data we're migrating from:
      # {"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",        # Unique product identifier
      # "GTIN"=>"9588853950167",                            # Global Trade Item Number (barcode)
      # "IPGLN"=>"9588853950006",                           # International Product Group Location Number (links to company)
      # "ISDRAFT"=>"N",                                     # Whether product is in draft status (Y/N)
      # "ISBASE"=>"BASE",                                   # Product type indicator
      # "CLFNID"=>nil,                                      # Classification ID
      # "PDGROSWGT"=>1371.0,                               # Product gross weight
      # "PDPKGWGT"=>nil,                                    # Package weight
      # "PDWGTUOM"=>"GRM",                                  # Weight unit of measure (GRM=grams)
      # "PDNETCONT"=>1000.0,                               # Net content amount
      # "PDNETCONTUOM"=>"MLT",                             # Net content unit of measure (MLT=milliliters)
      # "PCNTALCHBYVOL"=>nil,                              # Alcohol percentage by volume
      # "PDRCYC"=>nil,                                      # Product recycling information
      # "PDOUTRPKGRTRN"=>nil,                              # Outer package return information
      # "PDOUTRPKGRCYC"=>nil,                              # Outer package recycling information
      # "PDIGDT"=>nil,                                      # Product ingredient date
      # "PDGNMD"=>nil,                                      # Product genetically modified indicator
      # "FRSHDATEPD"=>nil,                                 # Fresh date period
      # "PDBCD"=>nil,                                       # Product barcode
      # "PDPRICED"=>nil,                                    # Product price
      # "MTRLSAFESHEET"=>nil,                              # Material safety sheet
      # "DNGRGOODITEMNOLTR"=>nil,                          # Dangerous goods item number
      # "DNGRGOODSBSTID"=>nil,                             # Dangerous goods substance ID
      # "DNGRGOODTECHNAME"=>nil,                           # Dangerous goods technical name
      # "PALTTYPECD"=>nil,                                 # Pallet type code
      # "PALTHNDL"=>nil,                                   # Pallet handling
      # "VARWGTTRDEITEM"=>nil,                             # Variable weight trade item
      # "PDORDRUNITIND"=>nil,                              # Product order unit indicator
      # "PDORDRQTYMIN"=>nil,                               # Product order quantity minimum
      # "PDORDRQTYMTPL"=>nil,                              # Product order quantity multiple
      # "RIGTOFRTN"=>nil,                                  # Right to return
      # "NONPUBIND"=>"1",                                  # Non-public indicator (1=non-public)
      # "INCLEXCLIND"=>nil,                                # Include/exclude indicator
      # "RLSEDT"=>nil,                                     # Release date
      # "STRTAVILDT"=>nil,                                 # Start availability date
      # "MFGRGLN"=>nil,                                    # Manufacturer GLN
      # "MFGRNAME"=>nil,                                   # Manufacturer name
      # "PDCNTYOFORGN"=>"MO",                              # Product country of origin (MO=Macau)
      # "PDGRPID"=>nil,                                    # Product group ID
      # "SUPYNO"=>nil,                                     # Supply number
      # "HMZDSYSCD"=>nil,                                  # Hazardous system code
      # "NABCAPDCD"=>nil,                                  # NABC APD code
      # "PDDEEP"=>nil,                                     # Product depth dimension
      # "PDHIGH"=>nil,                                     # Product height dimension
      # "PDWIDE"=>nil,                                     # Product width dimension
      # "PDDMTR"=>nil,                                     # Product diameter
      # "PDSIZEUOM"=>nil,                                     # Product size unit of measure
      # "PDOUTRPKGTYPE"=>nil,                              # Outer package type
      # "NOOFCMPTLAYR"=>nil,                               # Number of complete layers
      # "NOOFTRDEITEMINLAYR"=>nil,                         # Number of trade items in layer
      # "NOOFLAYRPERPALT"=>nil,                            # Number of layers per pallet
      # "NOOFTRDEITEMPERPALTLAYR"=>nil,                    # Number of trade items per pallet layer
      # "NOOFTRDEITEMPERPALT"=>nil,                        # Number of trade items per pallet
      # "HAZDCD"=>nil,                                     # Hazard code
      # "HAZDTYPECLFNSYST"=>nil,                           # Hazard type classification system
      # "PDHNDLINFO"=>nil,                                 # Product handling information
      # "STRGHNDLTEMPMAX"=>nil,                            # Storage handling temperature maximum
      # "STRGHNDLTEMPMIN"=>nil,                            # Storage handling temperature minimum
      # "STRGHNDLTEMPUOM"=>nil,                            # Storage handling temperature unit of measure
      # "MINLIFETIME"=>nil,                                # Minimum lifetime
      # "LEADTIME"=>nil,                                   # Lead time
      # "LASTMDFUSER"=>"9588853950006_admin",              # Last modification user
      # "LASTMDFDATE"=>2022-04-04 23:15:41 +0800,         # Last modification date
      # "VERSION"=>0,                                      # Record version for optimistic locking
      # "ISBRANDOWNER"=>nil,                               # Is brand owner indicator
      # "ISMFGR"=>nil,                                     # Is manufacturer indicator
      # "ISDISTRIBUTOR"=>nil,                              # Is distributor indicator
      # "ISRETAILER"=>nil,                                 # Is retailer indicator
      # "DNGRGOODTYPE"=>nil,                               # Dangerous goods type
      # "PDVLMPU"=>nil,                                    # Product volume per unit
      # "PDVLMUOM"=>nil,                                   # Product volume unit of measure
      # "MFGRADDR"=>nil,                                   # Manufacturer address
      # "MFGRTEL"=>nil,                                    # Manufacturer telephone
      # "MFGRFAX"=>nil,                                    # Manufacturer fax
      # "MFGRMAIL"=>nil,                                   # Manufacturer email
      # "MFGRSITE"=>nil,                                   # Manufacturer website
      # "DSTBNAME"=>nil,                                   # Distributor name
      # "DSTBADDR"=>nil,                                   # Distributor address
      # "DSTBTEL"=>nil,                                    # Distributor telephone
      # "DSTBFAX"=>nil,                                    # Distributor fax
      # "DSTBMAIL"=>nil,                                   # Distributor email
      # "DSTBSITE"=>nil,                                   # Distributor website
      # "CREATEDATE"=>2022-04-04 23:15:41 +0800,          # Record creation date
      # "NONBIZIND"=>"1",                                  # Non-business indicator
      # "OLD_CLFNCD"=>nil,                                 # Old classification code
      # "GTIN14"=>"09588853950167",                        # 14-digit GTIN (with leading zero)
      # "CLFNCDAGCY"=>nil,                                 # Classification code agency
      # "TGTMKTCNTYCD"=>nil,                               # Target market country code
      # "ENDAVILDT"=>nil,                                  # End availability date
      # "EFFCHGDT"=>nil,                                   # Effective change date
      # "PDGRPIDMTNCAGCY"=>nil,                            # Product group ID maintenance agency
      # "PDPKGWGTUOM"=>nil,                                # Product package weight unit of measure
      # "PDDNWGT"=>nil,                                       # Product drain weight
      # "PDDNWGTUOM"=>nil,                                 # Product drain weight unit of measure
      # "PDFATCONT"=>nil,                                  # Product fat content
      # "PDFATCONTUOM"=>nil,                               # Product fat content unit of measure
      # "PDFATCONTBOM"=>nil,                               # Product fat content basis of measurement
      # "PDFATCONTBOMUOM"=>nil,                            # Product fat content BOM unit of measure
      # "PEGHOZT"=>nil,                                    # Peg horizontal
      # "PEGHOZTUOM"=>nil,                                 # Peg horizontal unit of measure
      # "PEGVERT"=>nil,                                    # Peg vertical
      # "PEGVERTUOM"=>nil,                                 # Peg vertical unit of measure
      # "PDOUTRPKGMTRLTYPE"=>nil,                          # Product outer package material type
      # "PDPKGMTRLCD"=>nil,                                # Product package material code
      # "PDPKGMTRLCDLSTMTNCAGCY"=>nil,                     # Product package material code list maintenance agency
      # "STCKWGTMAXADMS"=>nil,                             # Stack weight maximum admissible
      # "STCKWGTUOM"=>nil,                                 # Stack weight unit of measure
      # "STCKFCTR"=>nil,                                   # Stack factor
      # "BASEGTIN"=>nil,                                   # Base GTIN
      # "PDCOLRCDVALUE"=>nil,                              # Product color code value
      # "PDCOLRCDLISTMTNCAGCY"=>nil,                       # Product color code list maintenance agency
      # "PDSIZECDVALUE"=>nil,                              # Product size code value
      # "PDSIZECDLISTMTNCAGCY"=>nil,                       # Product size code list maintenance agency
      # "PDFORM"=>nil,                                     # Product form
      # "PDSTNH"=>nil,                                     # Product strength
      # "PDSTNHUOM"=>nil,                                  # Product strength unit of measure
      # "PDSTNHBOM"=>nil,                                  # Product strength basis of measurement
      # "PDSTNHBOMUOM"=>nil,                               # Product strength BOM unit of measure
      # "PDINVUNITIND"=>nil,                               # Product invoice unit indicator
      # "PDDSPHUNITIND"=>nil,                              # Product dispatch unit indicator
      # "BTCHNO"=>nil,                                     # Batch number
      # "DEGOFORGWORT"=>nil,                               # Degree of original wort
      # "BASEPRICECONTVALUE"=>nil,                         # Base price content value
      # "BASEPRICECONTVALUEUOM"=>nil,                      # Base price content value unit of measure
      # "FRSTSALEDATE"=>nil,                               # First sale date
      # "LEVYKIND"=>nil,                                   # Levy kind
      # "LEVYCLAS"=>nil,                                   # Levy class
      # "LEVYAMT"=>nil,                                    # Levy amount
      # "PKGMTRLCMPSOFPD"=>nil,                            # Package material composition of product
      # "ORDRSIZEFCTR"=>nil,                               # Order size factor
      # "CUPNFMLYCD"=>nil,                                 # Coupon family code
      # "CUPNNO"=>nil,                                     # Coupon number
      # "LTRYGAMENO"=>nil,                                 # Lottery game number
      # "LTRYPACKBOOKNO"=>nil,                             # Lottery pack book number
      # "FLSHPTTEMP"=>nil,                                 # Flash point temperature
      # "TSDMARK"=>nil,                                    # TSD mark
      # "QMARK"=>nil,                                      # Q mark
      # "TOPBAND"=>nil,                                    # Top band
      # "GPC_CODE"=>"10000044",                            # Global Product Classification code
      # "RETAILCLASS"=>nil,                                # Retail class
      # "STATUS"=>"1",                                     # Product status (1=active)
      # "TRACEABILITY"=>nil}                               # Traceability information

      # STEP 1: Migrate base product data from tbl_pd
      puts "Step 1: Migrating base product data from tbl_pd..."

      # Process all products from the legacy database
      query_all_products = <<~SQL
        SELECT *
        FROM tbl_pd
      SQL
      stmt_all = db.query(query_all_products)

      product_count = 0
      stmt_all.each do |row|
        # Find existing product by legacy ID or create new one
        # This prevents duplicate entries during re-runs of the migration
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Link product to company using IPGLN (International Product Group Location Number)
        company = Company.find_by(ipgln: row['IPGLN'])
        if company
          p.company_id = company.id
        else
          puts "Warning: Company with IPGLN #{row['IPGLN']} not found for product #{row['PDID']}"
          next # Skip this product if company doesn't exist
        end

        # Map basic product information from legacy fields
        p.gtin = row['GTIN']                              # Global Trade Item Number (barcode)
        p.cross_weight = row['PDGROSWGT']                 # Product gross weight
        p.cross_weight_unit = row['PDWGTUOM']             # Weight unit of measure
        p.net_content = row['PDNETCONT']                  # Net content amount
        p.net_content_unit = row['PDNETCONTUOM']          # Net content unit of measure
        p.country_of_origin = row['PDCNTYOFORGN']         # Country of origin
        p.is_public_released = (row['NONPUBIND'] != '1')  # Convert non-public indicator to public flag

        # Store legacy tracking information for audit trail
        p.old_PDID = row['PDID']                          # Original product ID from legacy system
        p.old_LASTMDFUSER = row['LASTMDFUSER']            # Last modification user in legacy system
        p.old_LASTMDFDATE = row['LASTMDFDATE']            # Last modification date in legacy system
        p.old_CREATEDATE = row['CREATEDATE']              # Creation date in legacy system

        # Save the product record
        if p.save
          product_count += 1
          print "." if product_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 1 completed: #{product_count} products migrated from tbl_pd"

      # STEP 2: Overlay Traditional Chinese (zh_TW) translations from tbl_pd_ml
      puts "\nStep 2: Overlaying Traditional Chinese translations from tbl_pd_ml..."

      # Legacy tbl_pd_ml table structure (for reference)
      # Sample record showing Traditional Chinese product data:
      #{"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",        # Links to product in tbl_pd
      # "LANGID"=>"zh_TW",                                 # Language identifier (zh_TW, en, zh_CN)
      # "PDNAME_ML"=>"獅域·卡路 玫瑰味糖漿",                # Product name in specified language
      # "PDBRNDNAME_ML"=>"獅域·卡路",                      # Brand name in specified language
      # "PDDESC_ML"=>"用於調製各類飲品",                    # Product description in specified language
      # "PDPLUDESC_ML"=>nil,                               # Product plus description
      # "PDGRPIDDESC_ML"=>nil,                             # Product group ID description
      # "ADDATTRDESC_ML"=>nil,                             # Additional attribute description
      # "PDCOLRDESC_ML"=>nil,                              # Product color description
      # "PDSIZEDESC_ML"=>nil,                              # Product size description
      # "PDBRNDOWNER_ML"=>nil,                             # Brand owner name in specified language
      # "PDPDINGDT_ML"=>nil,                               # Product pending date
      # "PDGROSWGTDESC_ML"=>nil,                           # Product gross weight description
      # "PDUSEFUNC_ML"=>nil,                               # Product use function
      # "PDDRTUSG_ML"=>nil,                                # Product direct usage
      # "PDCAUTION_ML"=>nil,                               # Product caution/warning text
      # "PDSTRGDESC_ML"=>nil,                              # Product storage description
      # "PDBENEFIT_ML"=>nil,                               # Product benefit description
      # "PDREMARK_ML"=>nil,                                # Product remarks
      # "PDNETCONTDESC_ML"=>nil,                           # Product net content description
      # "PDPKGMTRLDESC"=>nil,                              # Product package material description
      # "PDCATDESC_ML"=>nil}                               # Product category description
      # Query for Traditional Chinese translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Process Traditional Chinese translations
      zh_tw_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set Traditional Chinese (default) fields
        # These become the primary fields since zh_TW is the default language
        p.name = row['PDNAME_ML']                         # Product name in Traditional Chinese
        p.brand_name = row['PDBRNDNAME_ML']               # Brand name in Traditional Chinese
        p.description = row['PDDESC_ML']                  # Product description in Traditional Chinese
        p.brand_owner_name = row['PDBRNDOWNER_ML']        # Brand owner name in Traditional Chinese
        p.use_function = row['PDUSEFUNC_ML']              # Product use function in Traditional Chinese
        p.caution = row['PDCAUTION_ML']                   # Product caution/warning in Traditional Chinese
        p.remark = row['PDREMARK_ML']                     # Product remarks in Traditional Chinese

        # Save the updated product record
        if p.save
          zh_tw_count += 1
          print "." if zh_tw_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_TW data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 2 completed: #{zh_tw_count} products updated with Traditional Chinese translations"
      # STEP 3: Overlay Simplified Chinese (zh_CN) translations from tbl_pd_ml
      puts "\nStep 3: Overlaying Simplified Chinese translations from tbl_pd_ml..."

      # Query for Simplified Chinese translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)

      # Process Simplified Chinese translations
      zh_cn_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set Simplified Chinese specific fields
        p.name_cn = row['PDNAME_ML']                      # Product name in Simplified Chinese
        p.brand_name_cn = row['PDBRNDNAME_ML']            # Brand name in Simplified Chinese
        p.description_cn = row['PDDESC_ML']               # Product description in Simplified Chinese
        p.brand_owner_name_cn = row['PDBRNDOWNER_ML']     # Brand owner name in Simplified Chinese
        p.caution_cn = row['PDCAUTION_ML']                # Product caution/warning in Simplified Chinese
        p.remark_cn = row['PDREMARK_ML']                  # Product remarks in Simplified Chinese

        # Save the updated product record
        if p.save
          zh_cn_count += 1
          print "." if zh_cn_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_CN data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 3 completed: #{zh_cn_count} products updated with Simplified Chinese translations"

      # STEP 4: Overlay English (en) translations from tbl_pd_ml
      puts "\nStep 4: Overlaying English translations from tbl_pd_ml..."

      # Query for English translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)

      # Process English translations
      en_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set English specific fields
        p.name_en = row['PDNAME_ML']                      # Product name in English
        p.brand_name_en = row['PDBRNDNAME_ML']            # Brand name in English
        p.description_en = row['PDDESC_ML']               # Product description in English
        p.brand_owner_name_en = row['PDBRNDOWNER_ML']     # Brand owner name in English
        p.caution_en = row['PDCAUTION_ML']                # Product caution/warning in English
        p.remark_en = row['PDREMARK_ML']                  # Product remarks in English

        # Save the updated product record
        if p.save
          en_count += 1
          print "." if en_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save English data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 4 completed: #{en_count} products updated with English translations"
      puts "\nProduct migration completed successfully!"
      puts "Total products processed: #{product_count}"
      puts "- Traditional Chinese translations: #{zh_tw_count}"
      puts "- Simplified Chinese translations: #{zh_cn_count}"
      puts "- English translations: #{en_count}"

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end

  desc "Migrate user data from legacy database to new schema"

  # Task: old_db_migrate_users
  # Purpose: Migrates user records from the legacy tbl_user table
  # to the new users table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_user: Contains user account information and authentication data
  #
  # Migration Strategy:
  # 1. Migrate user data from tbl_user
  # 2. Link users to companies using CMPYID matching
  # 3. Convert legacy password hashes and user permissions
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_USERID
  task old_db_migrate_users: :environment do
    puts "Starting user migration from legacy database..."
    puts "This task migrates user data from tbl_user table"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Old host: '************', New host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # Query all users from the legacy database
      puts "Migrating user data from tbl_user..."
      query = <<~SQL
        SELECT * FROM tbl_user
      SQL
      stmt = db.query(query)

      # Legacy tbl_user table structure (for reference)
      # Sample record showing the structure of user data we're migrating from:
      # {"USERID"=>"fd59c525e3de19c5c344955aa16eed49",        # Unique user identifier
      # "CMPYID"=>"f9e36bae8a0d60f2a28f5bc599e51ce7",        # Company ID (links to tbl_company)
      # "USERNAME"=>"9588818280001_admin",                    # Username for login
      # "GENDER"=>nil,                                        # User gender
      # "JOBTITLE"=>nil,                                      # Job title
      # "TITLE"=>nil,                                         # Personal title (Mr., Ms., etc.)
      # "EMAIL"=>"<EMAIL>",                # Email address
      # "MOBILE"=>nil,                                        # Mobile phone number
      # "TELNUM"=>nil,                                        # Telephone number
      # "BIRTHDAY"=>nil,                                      # Date of birth
      # "ISRECMAIL"=>nil,                                     # Whether to receive email notifications
      # "ISADMIN"=>"1",                                       # Whether user is admin (1=yes, 0=no)
      # "USERTYPE"=>"1",                                      # User type classification
      # "PASSWORD"=>"de6ee4771003bc3ed3a40567c5bc85f0f80607c86197bdf359045e4c81347ddb", # Encrypted password
      # "PASSWORDHINT"=>nil,                                  # Password hint
      # "ISFIRSTLOGON"=>"Y",                                  # Whether this is first login (Y/N)
      # "USERSTATUS"=>1,                                      # User status (1=active)
      # "ACTIVECODE"=>nil,                                    # Account activation code
      # "SESSIONID"=>nil,                                     # Current session ID
      # "LASTLOGINTIME"=>2020-08-14 10:05:53 +0800,         # Last login timestamp
      # "STARTTIME"=>nil,                                     # Session start time
      # "ENDTIME"=>nil,                                       # Session end time
      # "IPADDR"=>nil,                                        # Last login IP address
      # "SESSIONSTATUS"=>nil,                                 # Current session status
      # "CREATEDATE"=>2019-07-12 00:00:00 +0800,            # Account creation date
      # "REMARK"=>nil,                                        # Additional remarks
      # "LASTMDFUSER"=>"IMPROT",                             # Last user who modified the record
      # "LASTMDFDATE"=>2024-01-23 18:37:58 +0800,            # Last modification date
      # "VERSION"=>0,                                        # Record version for optimistic locking
      # "MARTIALSTAT"=>nil,                                  # Marital status
      # "NUMCHILD"=>nil,                                     # Number of children
      # "EDULEVEL"=>nil,                                     # Education level
      # "OCCUPATION"=>nil,                                   # Occupation
      # "HOW2KNOW"=>nil,                                     # How user learned about the service
      # "LOCKCOUNT"=>0,                                      # Number of failed login attempts
      # "LOCKTIME"=>nil}                                     # Account lock time

      # Process each user record from the legacy database
      user_count = 0
      stmt.each do |row|
        # Find existing user by legacy ID or create new one
        # This prevents duplicate entries during re-runs of the migration
        u = User.find_or_initialize_by(old_USERID: row['USERID'])

        # Link user to company using legacy CMPYID
        company = Company.find_by(old_CMPYID: row['CMPYID'])
        if company.nil?
          puts "Warning: Company not found for user #{row['EMAIL']} (CMPYID: #{row['CMPYID']})"
          next # Skip this user if company doesn't exist
        else
          # Map user information from legacy fields
          u.company_id = company.id                         # Link to migrated company
          u.email = row['EMAIL']                            # Email address (used for login)
          u.username = row['USERNAME']                      # Legacy username
          u.title = row['TITLE']                            # Personal title
          u.gender = row['GENDER']                          # Gender
          u.job_title = row['JOBTITLE']                     # Job title
          u.tel_num = row['TELNUM'] || row['MOBILE']        # Phone number (prefer TELNUM, fallback to MOBILE)
          u.remark = row['REMARK']                          # Additional remarks

          # Set user permissions based on legacy flags
          u.is_system_admin = (row['ISADMIN'] == '1')       # Convert admin flag to boolean
          u.is_user_management = (row['USERTYPE'] == '1')   # Convert user type to management flag

          # Store legacy authentication information for reference
          u.old_PASSWORD = row['PASSWORD']                  # Legacy encrypted password
          u.old_PASSWORDHINT = row['PASSWORDHINT']          # Legacy password hint
          u.old_USERNAME = row['USERNAME']                  # Legacy username
          u.old_USERID = row['USERID']                      # Legacy user ID

          # Set a temporary password (users will need to reset)
          # This is a secure random password that users cannot guess
          u.password = "TempMigration#{SecureRandom.hex(32)}!"

          # Save the user record
          if u.save
            user_count += 1
            print "." if user_count % 10 == 0  # Progress indicator
          else
            puts "Failed to save user #{row['EMAIL']}: #{u.errors.full_messages.join(', ')}"
          end
        end
      end
      puts "\nUser migration completed: #{user_count} users migrated from tbl_user"

      # OPTIONAL: Process multilingual user data from tbl_user_ml
      # This table contains user names in different languages
      puts "\nChecking for multilingual user data in tbl_user_ml..."

      # Legacy tbl_user_ml table structure (for reference)
      # Sample record showing multilingual user data:
      # {"USERID"=>"0",                                      # Links to user in tbl_user
      # "LANGID"=>"zh_TW",                                   # Language identifier
      # "FIRSTNAME_ML"=>"System",                            # First name in specified language
      # "LASTNAME_ML"=>"Admin",                              # Last name in specified language
      # "ADDR1_ML"=>nil,                                     # Address line 1 in specified language
      # "ADDR2_ML"=>nil,                                     # Address line 2 in specified language
      # "ADDR3_ML"=>nil,                                     # Address line 3 in specified language
      # "CITY_ML"=>nil,                                      # City in specified language
      # "COUNTRY_ML"=>nil}                                   # Country in specified language

      query = <<~SQL
        SELECT * FROM tbl_user_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Check if there's any multilingual user data to process
      ml_count = 0
      stmt.each do |row|
        ml_count += 1
        # Note: This section is currently just checking for data existence
        # Full implementation would process first_name, last_name, etc.
        # based on the application's multilingual user requirements
      end

      if ml_count > 0
        puts "Found #{ml_count} multilingual user records (processing not implemented yet)"
      else
        puts "No multilingual user data found"
      end

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end
end