# Namespace for legacy database migration tasks
# These tasks handle the migration of data from the old BarcodePlus system
# to the new Rails application database structure
namespace :old_db do
  desc "Migrate company data from legacy database to new schema"

  # Task: old_db_migrate_companies
  # Purpose: Migrates company records from the legacy tbl_company and tbl_company_ml tables
  # to the new companies table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_company: Contains main company information
  # - tbl_company_ml: Contains multilingual company information (zh_TW, en, zh_CN)
  #
  # Migration Strategy:
  # 1. First migrate base company data from tbl_company
  # 2. Then overlay Traditional Chinese (zh_TW) translations
  # 3. Finally overlay English (en) translations
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_CMPYID
  task old_db_migrate_companies: :environment do
    puts "Starting company migration from legacy database..."
    puts "This task migrates company data from tbl_company and tbl_company_ml tables"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************', # Alternative host: '************'
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # STEP 1: Migrate base company data from tbl_company
      # This table contains the primary company information without translations
      puts "Step 1: Migrating base company data from tbl_company..."
      query = <<~SQL
        SELECT *
        FROM tbl_company
      SQL
      stmt = db.query(query)

      # Legacy tbl_company table structure (for reference)
      # Sample record showing the structure of data we're migrating from:
      #{"CMPYID"=>"fee88f869754360e07f4bb600b152e44",        # Unique company identifier
      # "ACTYPE"=>0,                                         # Account type
      # "MEMBTYPE"=>nil,                                     # Membership type
      # "SUBMITDATE"=>2019-07-12 00:00:00 +0800,           # Date company was submitted/registered
      # "SUSPENDDATE"=>nil,                                  # Date company was suspended (if any)
      # "STATUS"=>"1",                                       # Company status (1=active)
      # "CRTSYS"=>"2",                                       # Creation system identifier
      # "TEL"=>"********",                                   # Primary telephone number
      # "FAX"=>nil,                                          # Fax number
      # "EMAIL"=>"<EMAIL>",                          # Primary email address
      # "URL"=>nil,                                          # Company website URL
      # "ISPUBLICSEARCHABLE"=>nil,                          # Whether company is publicly searchable
      # "IPGLN"=>"*************",                           # International Product Group Location Number
      # "EXPIRYDATE"=>nil,                                   # Membership expiry date
      # "AUTOGEN"=>"Y",                                      # Whether IPGLN was auto-generated
      # "COUNTRY"=>nil,                                      # Country code
      # "INCHARGEPERSONTITLE"=>nil,                         # Title of person in charge
      # "INCHARGE_EMAIL"=>nil,                              # Email of person in charge
      # "INCHARGE_PHONE"=>nil,                              # Phone of person in charge
      # "INCHARGE_FAX"=>nil,                                # Fax of person in charge
      # "CONTACTTITLE"=>nil,                                # Contact person title
      # "CONTACT_EMAIL"=>"<EMAIL>",                  # Contact person email
      # "CONTACT_PHONE"=>"********",                        # Contact person phone
      # "CONTACT_FAX"=>nil,                                 # Contact person fax
      # "REMARK"=>nil,                                       # Additional remarks
      # "LASTMDFUSER"=>"IMPORT",                            # Last user who modified the record
      # "LASTMDFDATE"=>2019-07-12 00:00:00 +0800,          # Last modification date
      # "VERSION"=>0,                                        # Record version for optimistic locking
      # "NONPUBIND"=>"1",                                   # Non-public indicator
      # "CCSEXPIRY"=>nil,                                   # CCS expiry date
      # "CRMNO"=>nil}                                       # CRM number

      # Process each company record from the legacy tbl_company table
      company_count = 0
      stmt.each do |row|
          # Find existing company by legacy ID or create new one
          # This prevents duplicate entries during re-runs of the migration
          c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

          # Map basic company information from legacy fields
          c.ipgln = row['IPGLN']                    # International Product Group Location Number
          # Note: PREFIX, NAME fields don't exist in legacy tbl_company - they come from tbl_company_ml
          # c.prefix = row['PREFIX']                # Will be set from multilingual table
          # c.name = row['NAME']                    # Will be set from multilingual table
          # c.name_cn = row['NAME_CN']              # Will be set from multilingual table
          # c.name_en = row['NAME_EN']              # Will be set from multilingual table

          # Contact information available in base table
          c.email = row['EMAIL']                    # Primary company email
          c.website_url = row['URL']                # Company website URL
          c.telephone = row['TEL']                  # Primary telephone number
          c.fax = row['FAX']                        # Fax number

          # Address fields don't exist in base table - they come from tbl_company_ml
          # c.location = row['LOCATION']            # Will be set from multilingual table
          # c.location_cn = row['LOCATION_CN']      # Will be set from multilingual table
          # c.location_en = row['LOCATION_EN']      # Will be set from multilingual table
          # c.floor = row['FLOOR']                  # Will be set from multilingual table
          # c.floor_cn = row['FLOOR_CN']            # Will be set from multilingual table
          # c.floor_en = row['FLOOR_EN']            # Will be set from multilingual table
          # c.street = row['STREET']                # Will be set from multilingual table
          # c.street_cn = row['STREET_CN']          # Will be set from multilingual table
          # c.street_en = row['STREET_EN']          # Will be set from multilingual table
          # c.city = row['CITY']                    # Will be set from multilingual table
          # c.city_cn = row['CITY_CN']              # Will be set from multilingual table
          # c.city_en = row['CITY_EN']              # Will be set from multilingual table
          # c.country = row['COUNTRY']              # Available in base table but often null

          # Person in charge information (limited in base table)
          # c.in_charge_person_name = row['IN_CHARGE_PERSON_NAME']     # Will be set from multilingual table
          # c.in_charge_person_name_cn = row['IN_CHARGE_PERSON_NAME_CN'] # Will be set from multilingual table
          # c.in_charge_person_name_en = row['IN_CHARGE_PERSON_NAME_EN'] # Will be set from multilingual table
          # c.in_charge_job_title = row['IN_CHARGE_JOB_TITLE']         # Will be set from multilingual table
          c.in_charge_email = row['INCHARGE_EMAIL']                   # Person in charge email
          c.in_charge_telephone = row['INCHARGE_PHONE']               # Person in charge phone

          # Contact person information (limited in base table)
          # c.contact_person_name = row['CONTACT_PERSON_NAME']         # Will be set from multilingual table
          # c.contact_person_name_cn = row['CONTACT_PERSON_NAME_CN']   # Will be set from multilingual table
          # c.contact_person_name_en = row['CONTACT_PERSON_NAME_EN']   # Will be set from multilingual table
          # c.contact_job_title = row['CONTACT_JOB_TITLE']             # Will be set from multilingual table
          c.contact_email = row['CONTACT_EMAIL']                      # Contact person email
          c.contact_telephone = row['CONTACT_PHONE']                  # Contact person phone
          # Administrator information (not available in base table)
          # c.admin_first_name = row['ADMIN_FIRST_NAME']               # Will be set separately if needed
          # c.admin_first_name_cn = row['ADMIN_FIRST_NAME_CN']         # Will be set separately if needed
          # c.admin_first_name_en = row['ADMIN_FIRST_NAME_EN']         # Will be set separately if needed
          # c.admin_last_name = row['ADMIN_LAST_NAME']                 # Will be set separately if needed
          # c.admin_last_name_cn = row['ADMIN_LAST_NAME_CN']           # Will be set separately if needed
          # c.admin_last_name_en = row['ADMIN_LAST_NAME_EN']           # Will be set separately if needed
          # c.admin_title = row['ADMIN_TITLE']                         # Will be set separately if needed

          # Store legacy tracking information for audit trail
          c.old_CMPYID = row['CMPYID']              # Original company ID from legacy system
          c.old_LASTMDFUSER = row['LASTMDFUSER']    # Last modification user in legacy system
          c.old_LASTMDFDATE = row['LASTMDFDATE']    # Last modification date in legacy system

          # Save the company record
          if c.save
            company_count += 1
            print "." if company_count % 10 == 0  # Progress indicator
          else
            puts "Failed to save company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
          end
      end
      puts "\nStep 1 completed: #{company_count} companies migrated from tbl_company"

      # STEP 2: Overlay Traditional Chinese (zh_TW) translations from tbl_company_ml
      # This table contains multilingual company information
      puts "\nStep 2: Overlaying Traditional Chinese translations from tbl_company_ml..."

      # Legacy tbl_company_ml table structure (for reference)
      # Sample record showing Traditional Chinese data:
      #{"CMPYID"=>"4fc58c542fa62e4c0ca5018e600216b4",        # Links to company in tbl_company
      # "LANGID"=>"zh_TW",                                   # Language identifier (zh_TW, en, zh_CN)
      # "CMPYNAME_ML"=>"澳门润澳生物科技有限公司",              # Company name in specified language
      # "ADDR1_ML"=>"南方大厦",                              # Address line 1 (building/location)
      # "ADDR2_ML"=>"4楼A座",                               # Address line 2 (floor)
      # "ADDR3_ML"=>"友谊大马路1017号",                       # Address line 3 (street)
      # "CITY_ML"=>"澳门",                                   # City name in specified language
      # "INCHARGEPERSONNAME_ML"=>"石翰中",                   # Person in charge name in specified language
      # "CONTACTNAME_ML"=>"石翰中",                          # Contact person name in specified language
      # "ID"=>1398}                                          # Unique record ID for this translation

      # Query for Traditional Chinese translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_TW'
      SQL
      stmt = db.query(query)

      # Process Traditional Chinese translations
      zh_tw_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set Traditional Chinese (default) fields
        # These become the primary fields since zh_TW is the default language
        c.name = row['CMPYNAME_ML']                         # Company name in Traditional Chinese
        c.location = row['ADDR1_ML']                        # Building/location name in Traditional Chinese
        c.floor = row['ADDR2_ML']                           # Floor information in Traditional Chinese
        c.street = row['ADDR3_ML']                          # Street address in Traditional Chinese
        c.city = row['CITY_ML']                             # City name in Traditional Chinese
        c.in_charge_person_name = row['INCHARGEPERSONNAME_ML']  # Person in charge name in Traditional Chinese
        c.contact_person_name = row['CONTACTNAME_ML']       # Contact person name in Traditional Chinese

        # Save the updated company record
        if c.save
          zh_tw_count += 1
          print "." if zh_tw_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_TW data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 2 completed: #{zh_tw_count} companies updated with Traditional Chinese translations"

      # STEP 3: Overlay Simplified Chinese (zh_CN) translations from tbl_company_ml
      puts "\nStep 3: Overlaying Simplified Chinese translations from tbl_company_ml..."

      # Query for Simplified Chinese translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)

      # Process Simplified Chinese translations
      zh_cn_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set Simplified Chinese specific fields
        c.name_cn = row['CMPYNAME_ML']                         # Company name in Simplified Chinese
        c.location_cn = row['ADDR1_ML']                        # Building/location name in Simplified Chinese
        c.floor_cn = row['ADDR2_ML']                           # Floor information in Simplified Chinese
        c.street_cn = row['ADDR3_ML']                          # Street address in Simplified Chinese
        c.city_cn = row['CITY_ML']                             # City name in Simplified Chinese
        c.in_charge_person_name_cn = row['INCHARGEPERSONNAME_ML']  # Person in charge name in Simplified Chinese
        c.contact_person_name_cn = row['CONTACTNAME_ML']       # Contact person name in Simplified Chinese

        # Save the updated company record
        if c.save
          zh_cn_count += 1
          print "." if zh_cn_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_CN data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 3 completed: #{zh_cn_count} companies updated with Simplified Chinese translations"

      # STEP 4: Overlay English (en) translations from tbl_company_ml
      puts "\nStep 4: Overlaying English translations from tbl_company_ml..."

      # Query for English translations
      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)

      # Process English translations
      en_count = 0
      stmt.each do |row|
        # Find the company record that was created in Step 1
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        # Set English specific fields
        c.name_en = row['CMPYNAME_ML']                         # Company name in English
        c.location_en = row['ADDR1_ML']                        # Building/location name in English
        c.floor_en = row['ADDR2_ML']                           # Floor information in English
        c.street_en = row['ADDR3_ML']                          # Street address in English
        c.city_en = row['CITY_ML']                             # City name in English
        c.in_charge_person_name_en = row['INCHARGEPERSONNAME_ML']  # Person in charge name in English
        c.contact_person_name_en = row['CONTACTNAME_ML']       # Contact person name in English

        # Save the updated company record
        if c.save
          en_count += 1
          print "." if en_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save English data for company #{row['CMPYID']}: #{c.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 4 completed: #{en_count} companies updated with English translations"
      puts "\nCompany migration completed successfully!"
      puts "Total companies processed: #{company_count}"
      puts "- Traditional Chinese translations: #{zh_tw_count}"
      puts "- Simplified Chinese translations: #{zh_cn_count}"
      puts "- English translations: #{en_count}"

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end

  desc "Migrate product data from legacy database to new schema"

  # Task: old_db_migrate_products
  # Purpose: Migrates product records from the legacy tbl_pd and tbl_pd_ml tables
  # to the new products table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_pd: Contains main product information
  # - tbl_pd_ml: Contains multilingual product information (zh_TW, en, zh_CN)
  #
  # Migration Strategy:
  # 1. First migrate base product data from tbl_pd
  # 2. Then overlay multilingual translations from tbl_pd_ml
  # 3. Link products to companies using IPGLN matching
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_PDID
  task old_db_migrate_products: :environment do
    puts "Starting product migration from legacy database..."
    puts "This task migrates product data from tbl_pd and tbl_pd_ml tables"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************',
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # First, examine the structure of the legacy tbl_pd table
      puts "Examining legacy tbl_pd table structure..."
      query = <<~SQL
        SELECT *
        FROM tbl_pd
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Legacy tbl_pd table structure (for reference)
      # Sample record showing the structure of product data we're migrating from:
      # {"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",        # Unique product identifier
      # "GTIN"=>"9588853950167",                            # Global Trade Item Number (barcode)
      # "IPGLN"=>"9588853950006",                           # International Product Group Location Number (links to company)
      # "ISDRAFT"=>"N",                                     # Whether product is in draft status (Y/N)
      # "ISBASE"=>"BASE",                                   # Product type indicator
      # "CLFNID"=>nil,                                      # Classification ID
      # "PDGROSWGT"=>1371.0,                               # Product gross weight
      # "PDPKGWGT"=>nil,                                    # Package weight
      # "PDWGTUOM"=>"GRM",                                  # Weight unit of measure (GRM=grams)
      # "PDNETCONT"=>1000.0,                               # Net content amount
      # "PDNETCONTUOM"=>"MLT",                             # Net content unit of measure (MLT=milliliters)
      # "PCNTALCHBYVOL"=>nil,                              # Alcohol percentage by volume
      # "PDRCYC"=>nil,                                      # Product recycling information
      # "PDOUTRPKGRTRN"=>nil,                              # Outer package return information
      # "PDOUTRPKGRCYC"=>nil,                              # Outer package recycling information
      # "PDIGDT"=>nil,                                      # Product ingredient date
      # "PDGNMD"=>nil,                                      # Product genetically modified indicator
      # "FRSHDATEPD"=>nil,                                 # Fresh date period
      # "PDBCD"=>nil,                                       # Product barcode
      # "PDPRICED"=>nil,                                    # Product price
      # "MTRLSAFESHEET"=>nil,                              # Material safety sheet
      # "DNGRGOODITEMNOLTR"=>nil,                          # Dangerous goods item number
      # "DNGRGOODSBSTID"=>nil,                             # Dangerous goods substance ID
      # "DNGRGOODTECHNAME"=>nil,                           # Dangerous goods technical name
      # "PALTTYPECD"=>nil,                                 # Pallet type code
      # "PALTHNDL"=>nil,                                   # Pallet handling
      # "VARWGTTRDEITEM"=>nil,                             # Variable weight trade item
      # "PDORDRUNITIND"=>nil,                              # Product order unit indicator
      # "PDORDRQTYMIN"=>nil,                               # Product order quantity minimum
      # "PDORDRQTYMTPL"=>nil,                              # Product order quantity multiple
      # "RIGTOFRTN"=>nil,                                  # Right to return
      # "NONPUBIND"=>"1",                                  # Non-public indicator (1=non-public)
      # "INCLEXCLIND"=>nil,                                # Include/exclude indicator
      # "RLSEDT"=>nil,                                     # Release date
      # "STRTAVILDT"=>nil,                                 # Start availability date
      # "MFGRGLN"=>nil,                                    # Manufacturer GLN
      # "MFGRNAME"=>nil,                                   # Manufacturer name
      # "PDCNTYOFORGN"=>"MO",                              # Product country of origin (MO=Macau)
      # "PDGRPID"=>nil,                                    # Product group ID
      # "SUPYNO"=>nil,                                     # Supply number
      # "HMZDSYSCD"=>nil,                                  # Hazardous system code
      # "NABCAPDCD"=>nil,                                  # NABC APD code
      # "PDDEEP"=>nil,                                     # Product depth dimension
      # "PDHIGH"=>nil,                                     # Product height dimension
      # "PDWIDE"=>nil,                                     # Product width dimension
      # "PDDMTR"=>nil,                                     # Product diameter
      # "PDSIZEUOM"=>nil,                                     # Product size unit of measure
      # "PDOUTRPKGTYPE"=>nil,                              # Outer package type
      # "NOOFCMPTLAYR"=>nil,                               # Number of complete layers
      # "NOOFTRDEITEMINLAYR"=>nil,                         # Number of trade items in layer
      # "NOOFLAYRPERPALT"=>nil,                            # Number of layers per pallet
      # "NOOFTRDEITEMPERPALTLAYR"=>nil,                    # Number of trade items per pallet layer
      # "NOOFTRDEITEMPERPALT"=>nil,                        # Number of trade items per pallet
      # "HAZDCD"=>nil,                                     # Hazard code
      # "HAZDTYPECLFNSYST"=>nil,                           # Hazard type classification system
      # "PDHNDLINFO"=>nil,                                 # Product handling information
      # "STRGHNDLTEMPMAX"=>nil,                            # Storage handling temperature maximum
      # "STRGHNDLTEMPMIN"=>nil,                            # Storage handling temperature minimum
      # "STRGHNDLTEMPUOM"=>nil,                            # Storage handling temperature unit of measure
      # "MINLIFETIME"=>nil,                                # Minimum lifetime
      # "LEADTIME"=>nil,                                   # Lead time
      # "LASTMDFUSER"=>"9588853950006_admin",              # Last modification user
      # "LASTMDFDATE"=>2022-04-04 23:15:41 +0800,         # Last modification date
      # "VERSION"=>0,                                      # Record version for optimistic locking
      # "ISBRANDOWNER"=>nil,                               # Is brand owner indicator
      # "ISMFGR"=>nil,                                     # Is manufacturer indicator
      # "ISDISTRIBUTOR"=>nil,                              # Is distributor indicator
      # "ISRETAILER"=>nil,                                 # Is retailer indicator
      # "DNGRGOODTYPE"=>nil,                               # Dangerous goods type
      # "PDVLMPU"=>nil,                                    # Product volume per unit
      # "PDVLMUOM"=>nil,                                   # Product volume unit of measure
      # "MFGRADDR"=>nil,                                   # Manufacturer address
      # "MFGRTEL"=>nil,                                    # Manufacturer telephone
      # "MFGRFAX"=>nil,                                    # Manufacturer fax
      # "MFGRMAIL"=>nil,                                   # Manufacturer email
      # "MFGRSITE"=>nil,                                   # Manufacturer website
      # "DSTBNAME"=>nil,                                   # Distributor name
      # "DSTBADDR"=>nil,                                   # Distributor address
      # "DSTBTEL"=>nil,                                    # Distributor telephone
      # "DSTBFAX"=>nil,                                    # Distributor fax
      # "DSTBMAIL"=>nil,                                   # Distributor email
      # "DSTBSITE"=>nil,                                   # Distributor website
      # "CREATEDATE"=>2022-04-04 23:15:41 +0800,          # Record creation date
      # "NONBIZIND"=>"1",                                  # Non-business indicator
      # "OLD_CLFNCD"=>nil,                                 # Old classification code
      # "GTIN14"=>"09588853950167",                        # 14-digit GTIN (with leading zero)
      # "CLFNCDAGCY"=>nil,                                 # Classification code agency
      # "TGTMKTCNTYCD"=>nil,                               # Target market country code
      # "ENDAVILDT"=>nil,                                  # End availability date
      # "EFFCHGDT"=>nil,                                   # Effective change date
      # "PDGRPIDMTNCAGCY"=>nil,                            # Product group ID maintenance agency
      # "PDPKGWGTUOM"=>nil,                                # Product package weight unit of measure
      # "PDDNWGT"=>nil,                                       # Product drain weight
      # "PDDNWGTUOM"=>nil,                                 # Product drain weight unit of measure
      # "PDFATCONT"=>nil,                                  # Product fat content
      # "PDFATCONTUOM"=>nil,                               # Product fat content unit of measure
      # "PDFATCONTBOM"=>nil,                               # Product fat content basis of measurement
      # "PDFATCONTBOMUOM"=>nil,                            # Product fat content BOM unit of measure
      # "PEGHOZT"=>nil,                                    # Peg horizontal
      # "PEGHOZTUOM"=>nil,                                 # Peg horizontal unit of measure
      # "PEGVERT"=>nil,                                    # Peg vertical
      # "PEGVERTUOM"=>nil,                                 # Peg vertical unit of measure
      # "PDOUTRPKGMTRLTYPE"=>nil,                          # Product outer package material type
      # "PDPKGMTRLCD"=>nil,                                # Product package material code
      # "PDPKGMTRLCDLSTMTNCAGCY"=>nil,                     # Product package material code list maintenance agency
      # "STCKWGTMAXADMS"=>nil,                             # Stack weight maximum admissible
      # "STCKWGTUOM"=>nil,                                 # Stack weight unit of measure
      # "STCKFCTR"=>nil,                                   # Stack factor
      # "BASEGTIN"=>nil,                                   # Base GTIN
      # "PDCOLRCDVALUE"=>nil,                              # Product color code value
      # "PDCOLRCDLISTMTNCAGCY"=>nil,                       # Product color code list maintenance agency
      # "PDSIZECDVALUE"=>nil,                              # Product size code value
      # "PDSIZECDLISTMTNCAGCY"=>nil,                       # Product size code list maintenance agency
      # "PDFORM"=>nil,                                     # Product form
      # "PDSTNH"=>nil,                                     # Product strength
      # "PDSTNHUOM"=>nil,                                  # Product strength unit of measure
      # "PDSTNHBOM"=>nil,                                  # Product strength basis of measurement
      # "PDSTNHBOMUOM"=>nil,                               # Product strength BOM unit of measure
      # "PDINVUNITIND"=>nil,                               # Product invoice unit indicator
      # "PDDSPHUNITIND"=>nil,                              # Product dispatch unit indicator
      # "BTCHNO"=>nil,                                     # Batch number
      # "DEGOFORGWORT"=>nil,                               # Degree of original wort
      # "BASEPRICECONTVALUE"=>nil,                         # Base price content value
      # "BASEPRICECONTVALUEUOM"=>nil,                      # Base price content value unit of measure
      # "FRSTSALEDATE"=>nil,                               # First sale date
      # "LEVYKIND"=>nil,                                   # Levy kind
      # "LEVYCLAS"=>nil,                                   # Levy class
      # "LEVYAMT"=>nil,                                    # Levy amount
      # "PKGMTRLCMPSOFPD"=>nil,                            # Package material composition of product
      # "ORDRSIZEFCTR"=>nil,                               # Order size factor
      # "CUPNFMLYCD"=>nil,                                 # Coupon family code
      # "CUPNNO"=>nil,                                     # Coupon number
      # "LTRYGAMENO"=>nil,                                 # Lottery game number
      # "LTRYPACKBOOKNO"=>nil,                             # Lottery pack book number
      # "FLSHPTTEMP"=>nil,                                 # Flash point temperature
      # "TSDMARK"=>nil,                                    # TSD mark
      # "QMARK"=>nil,                                      # Q mark
      # "TOPBAND"=>nil,                                    # Top band
      # "GPC_CODE"=>"10000044",                            # Global Product Classification code
      # "RETAILCLASS"=>nil,                                # Retail class
      # "STATUS"=>"1",                                     # Product status (1=active)
      # "TRACEABILITY"=>nil}                               # Traceability information

      # STEP 1: Migrate base product data from tbl_pd
      puts "Step 1: Migrating base product data from tbl_pd..."

      # Process all products from the legacy database
      query_all_products = <<~SQL
        SELECT *
        FROM tbl_pd
      SQL
      stmt_all = db.query(query_all_products)

      product_count = 0
      stmt_all.each do |row|
        # Find existing product by legacy ID or create new one
        # This prevents duplicate entries during re-runs of the migration
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Link product to company using IPGLN (International Product Group Location Number)
        company = Company.find_by(ipgln: row['IPGLN'])
        if company
          p.company_id = company.id
        else
          puts "Warning: Company with IPGLN #{row['IPGLN']} not found for product #{row['PDID']}"
          next # Skip this product if company doesn't exist
        end

        # Map basic product information from legacy fields
        p.gtin = row['GTIN']                              # Global Trade Item Number (barcode)
        p.cross_weight = row['PDGROSWGT']                 # Product gross weight
        p.cross_weight_unit = row['PDWGTUOM']             # Weight unit of measure
        p.net_content = row['PDNETCONT']                  # Net content amount
        p.net_content_unit = row['PDNETCONTUOM']          # Net content unit of measure
        p.country_of_origin = row['PDCNTYOFORGN']         # Country of origin
        p.is_public_released = (row['NONPUBIND'] != '1')  # Convert non-public indicator to public flag

        # Store legacy tracking information for audit trail
        p.old_PDID = row['PDID']                          # Original product ID from legacy system
        p.old_LASTMDFUSER = row['LASTMDFUSER']            # Last modification user in legacy system
        p.old_LASTMDFDATE = row['LASTMDFDATE']            # Last modification date in legacy system
        p.old_CREATEDATE = row['CREATEDATE']              # Creation date in legacy system

        # Save the product record
        if p.save
          product_count += 1
          print "." if product_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 1 completed: #{product_count} products migrated from tbl_pd"

      # STEP 2: Overlay Traditional Chinese (zh_TW) translations from tbl_pd_ml
      puts "\nStep 2: Overlaying Traditional Chinese translations from tbl_pd_ml..."

      # Legacy tbl_pd_ml table structure (for reference)
      # Sample record showing Traditional Chinese product data:
      #{"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",        # Links to product in tbl_pd
      # "LANGID"=>"zh_TW",                                 # Language identifier (zh_TW, en, zh_CN)
      # "PDNAME_ML"=>"獅域·卡路 玫瑰味糖漿",                # Product name in specified language
      # "PDBRNDNAME_ML"=>"獅域·卡路",                      # Brand name in specified language
      # "PDDESC_ML"=>"用於調製各類飲品",                    # Product description in specified language
      # "PDPLUDESC_ML"=>nil,                               # Product plus description
      # "PDGRPIDDESC_ML"=>nil,                             # Product group ID description
      # "ADDATTRDESC_ML"=>nil,                             # Additional attribute description
      # "PDCOLRDESC_ML"=>nil,                              # Product color description
      # "PDSIZEDESC_ML"=>nil,                              # Product size description
      # "PDBRNDOWNER_ML"=>nil,                             # Brand owner name in specified language
      # "PDPDINGDT_ML"=>nil,                               # Product pending date
      # "PDGROSWGTDESC_ML"=>nil,                           # Product gross weight description
      # "PDUSEFUNC_ML"=>nil,                               # Product use function
      # "PDDRTUSG_ML"=>nil,                                # Product direct usage
      # "PDCAUTION_ML"=>nil,                               # Product caution/warning text
      # "PDSTRGDESC_ML"=>nil,                              # Product storage description
      # "PDBENEFIT_ML"=>nil,                               # Product benefit description
      # "PDREMARK_ML"=>nil,                                # Product remarks
      # "PDNETCONTDESC_ML"=>nil,                           # Product net content description
      # "PDPKGMTRLDESC"=>nil,                              # Product package material description
      # "PDCATDESC_ML"=>nil}                               # Product category description
      # Query for Traditional Chinese translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Process Traditional Chinese translations
      zh_tw_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set Traditional Chinese (default) fields
        # These become the primary fields since zh_TW is the default language
        p.name = row['PDNAME_ML']                         # Product name in Traditional Chinese
        p.brand_name = row['PDBRNDNAME_ML']               # Brand name in Traditional Chinese
        p.description = row['PDDESC_ML']                  # Product description in Traditional Chinese
        p.brand_owner_name = row['PDBRNDOWNER_ML']        # Brand owner name in Traditional Chinese
        p.use_function = row['PDUSEFUNC_ML']              # Product use function in Traditional Chinese
        p.caution = row['PDCAUTION_ML']                   # Product caution/warning in Traditional Chinese
        p.remark = row['PDREMARK_ML']                     # Product remarks in Traditional Chinese

        # Save the updated product record
        if p.save
          zh_tw_count += 1
          print "." if zh_tw_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_TW data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 2 completed: #{zh_tw_count} products updated with Traditional Chinese translations"
      # STEP 3: Overlay Simplified Chinese (zh_CN) translations from tbl_pd_ml
      puts "\nStep 3: Overlaying Simplified Chinese translations from tbl_pd_ml..."

      # Query for Simplified Chinese translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)

      # Process Simplified Chinese translations
      zh_cn_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set Simplified Chinese specific fields
        p.name_cn = row['PDNAME_ML']                      # Product name in Simplified Chinese
        p.brand_name_cn = row['PDBRNDNAME_ML']            # Brand name in Simplified Chinese
        p.description_cn = row['PDDESC_ML']               # Product description in Simplified Chinese
        p.brand_owner_name_cn = row['PDBRNDOWNER_ML']     # Brand owner name in Simplified Chinese
        p.caution_cn = row['PDCAUTION_ML']                # Product caution/warning in Simplified Chinese
        p.remark_cn = row['PDREMARK_ML']                  # Product remarks in Simplified Chinese

        # Save the updated product record
        if p.save
          zh_cn_count += 1
          print "." if zh_cn_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save zh_CN data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 3 completed: #{zh_cn_count} products updated with Simplified Chinese translations"

      # STEP 4: Overlay English (en) translations from tbl_pd_ml
      puts "\nStep 4: Overlaying English translations from tbl_pd_ml..."

      # Query for English translations
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)

      # Process English translations
      en_count = 0
      stmt.each do |row|
        # Find the product record that was created in Step 1
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        # Set English specific fields
        p.name_en = row['PDNAME_ML']                      # Product name in English
        p.brand_name_en = row['PDBRNDNAME_ML']            # Brand name in English
        p.description_en = row['PDDESC_ML']               # Product description in English
        p.brand_owner_name_en = row['PDBRNDOWNER_ML']     # Brand owner name in English
        p.caution_en = row['PDCAUTION_ML']                # Product caution/warning in English
        p.remark_en = row['PDREMARK_ML']                  # Product remarks in English

        # Save the updated product record
        if p.save
          en_count += 1
          print "." if en_count % 10 == 0  # Progress indicator
        else
          puts "Failed to save English data for product #{row['PDID']}: #{p.errors.full_messages.join(', ')}"
        end
      end
      puts "\nStep 4 completed: #{en_count} products updated with English translations"
      puts "\nProduct migration completed successfully!"
      puts "Total products processed: #{product_count}"
      puts "- Traditional Chinese translations: #{zh_tw_count}"
      puts "- Simplified Chinese translations: #{zh_cn_count}"
      puts "- English translations: #{en_count}"

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end

  desc "Migrate user data from legacy database to new schema"

  # Task: old_db_migrate_users
  # Purpose: Migrates user records from the legacy tbl_user table
  # to the new users table in the Rails application
  #
  # Legacy Database Structure:
  # - tbl_user: Contains user account information and authentication data
  #
  # Migration Strategy:
  # 1. Migrate user data from tbl_user
  # 2. Link users to companies using CMPYID matching
  # 3. Convert legacy password hashes and user permissions
  # 4. Uses find_or_initialize_by to prevent duplicates based on old_USERID
  task old_db_migrate_users: :environment do
    puts "Starting user migration from legacy database..."
    puts "This task migrates user data from tbl_user table"

    require 'mysql2'

    begin
      # Establish connection to legacy MySQL database
      # Note: Using readonly credentials for safety during migration
      puts "Connecting to legacy database..."
      db = Mysql2::Client.new(
          :host => '************',
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # Query all users from the legacy database
      puts "Migrating user data from tbl_user..."
      query = <<~SQL
        SELECT * FROM tbl_user
      SQL
      stmt = db.query(query)

      # Legacy tbl_user table structure (for reference)
      # Sample record showing the structure of user data we're migrating from:
      # {"USERID"=>"fd59c525e3de19c5c344955aa16eed49",        # Unique user identifier
      # "CMPYID"=>"f9e36bae8a0d60f2a28f5bc599e51ce7",        # Company ID (links to tbl_company)
      # "USERNAME"=>"9588818280001_admin",                    # Username for login
      # "GENDER"=>nil,                                        # User gender
      # "JOBTITLE"=>nil,                                      # Job title
      # "TITLE"=>nil,                                         # Personal title (Mr., Ms., etc.)
      # "EMAIL"=>"<EMAIL>",                # Email address
      # "MOBILE"=>nil,                                        # Mobile phone number
      # "TELNUM"=>nil,                                        # Telephone number
      # "BIRTHDAY"=>nil,                                      # Date of birth
      # "ISRECMAIL"=>nil,                                     # Whether to receive email notifications
      # "ISADMIN"=>"1",                                       # Whether user is admin (1=yes, 0=no)
      # "USERTYPE"=>"1",                                      # User type classification
      # "PASSWORD"=>"de6ee4771003bc3ed3a40567c5bc85f0f80607c86197bdf359045e4c81347ddb", # Encrypted password
      # "PASSWORDHINT"=>nil,                                  # Password hint
      # "ISFIRSTLOGON"=>"Y",                                  # Whether this is first login (Y/N)
      # "USERSTATUS"=>1,                                      # User status (1=active)
      # "ACTIVECODE"=>nil,                                    # Account activation code
      # "SESSIONID"=>nil,                                     # Current session ID
      # "LASTLOGINTIME"=>2020-08-14 10:05:53 +0800,         # Last login timestamp
      # "STARTTIME"=>nil,                                     # Session start time
      # "ENDTIME"=>nil,                                       # Session end time
      # "IPADDR"=>nil,                                        # Last login IP address
      # "SESSIONSTATUS"=>nil,                                 # Current session status
      # "CREATEDATE"=>2019-07-12 00:00:00 +0800,            # Account creation date
      # "REMARK"=>nil,                                        # Additional remarks
      # "LASTMDFUSER"=>"IMPROT",                             # Last user who modified the record
      # "LASTMDFDATE"=>2024-01-23 18:37:58 +0800,            # Last modification date
      # "VERSION"=>0,                                        # Record version for optimistic locking
      # "MARTIALSTAT"=>nil,                                  # Marital status
      # "NUMCHILD"=>nil,                                     # Number of children
      # "EDULEVEL"=>nil,                                     # Education level
      # "OCCUPATION"=>nil,                                   # Occupation
      # "HOW2KNOW"=>nil,                                     # How user learned about the service
      # "LOCKCOUNT"=>0,                                      # Number of failed login attempts
      # "LOCKTIME"=>nil}                                     # Account lock time

      # Process each user record from the legacy database
      user_count = 0
      stmt.each do |row|
        # Find existing user by legacy ID or create new one
        # This prevents duplicate entries during re-runs of the migration
        u = User.find_or_initialize_by(old_USERID: row['USERID'])

        # Link user to company using legacy CMPYID
        company = Company.find_by(old_CMPYID: row['CMPYID'])
        if company.nil?
          puts "Warning: Company not found for user #{row['EMAIL']} (CMPYID: #{row['CMPYID']})"
          next # Skip this user if company doesn't exist
        else
          # Map user information from legacy fields
          u.company_id = company.id                         # Link to migrated company
          u.email = row['EMAIL']                            # Email address (used for login)
          u.username = row['USERNAME']                      # Legacy username
          u.title = row['TITLE']                            # Personal title
          u.gender = row['GENDER']                          # Gender
          u.job_title = row['JOBTITLE']                     # Job title
          u.tel_num = row['TELNUM'] || row['MOBILE']        # Phone number (prefer TELNUM, fallback to MOBILE)
          u.remark = row['REMARK']                          # Additional remarks

          # Set user permissions based on legacy flags
          u.is_system_admin = (row['ISADMIN'] == '1')       # Convert admin flag to boolean
          u.is_user_management = (row['USERTYPE'] == '1')   # Convert user type to management flag

          # Store legacy authentication information for reference
          u.old_PASSWORD = row['PASSWORD']                  # Legacy encrypted password
          u.old_PASSWORDHINT = row['PASSWORDHINT']          # Legacy password hint
          u.old_USERNAME = row['USERNAME']                  # Legacy username
          u.old_USERID = row['USERID']                      # Legacy user ID

          # Set a temporary password (users will need to reset)
          # This is a secure random password that users cannot guess
          u.password = "TempMigration#{SecureRandom.hex(32)}!"

          # Save the user record
          if u.save
            user_count += 1
            print "." if user_count % 10 == 0  # Progress indicator
          else
            puts "Failed to save user #{row['EMAIL']}: #{u.errors.full_messages.join(', ')}"
          end
        end
      end
      puts "\nUser migration completed: #{user_count} users migrated from tbl_user"

      # OPTIONAL: Process multilingual user data from tbl_user_ml
      # This table contains user names in different languages
      puts "\nChecking for multilingual user data in tbl_user_ml..."

      # Legacy tbl_user_ml table structure (for reference)
      # Sample record showing multilingual user data:
      # {"USERID"=>"0",                                      # Links to user in tbl_user
      # "LANGID"=>"zh_TW",                                   # Language identifier
      # "FIRSTNAME_ML"=>"System",                            # First name in specified language
      # "LASTNAME_ML"=>"Admin",                              # Last name in specified language
      # "ADDR1_ML"=>nil,                                     # Address line 1 in specified language
      # "ADDR2_ML"=>nil,                                     # Address line 2 in specified language
      # "ADDR3_ML"=>nil,                                     # Address line 3 in specified language
      # "CITY_ML"=>nil,                                      # City in specified language
      # "COUNTRY_ML"=>nil}                                   # Country in specified language

      query = <<~SQL
        SELECT * FROM tbl_user_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)

      # Check if there's any multilingual user data to process
      ml_count = 0
      stmt.each do |row|
        ml_count += 1
        # Note: This section is currently just checking for data existence
        # Full implementation would process first_name, last_name, etc.
        # based on the application's multilingual user requirements
      end

      if ml_count > 0
        puts "Found #{ml_count} multilingual user records (processing not implemented yet)"
      else
        puts "No multilingual user data found"
      end

    # Error handling for database connection issues
    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      puts "Please check database connection settings and network connectivity."
      exit 1
    ensure
      # Always close the database connection to prevent connection leaks
      db.close if db
      puts "Database connection closed."
    end
  end
end