namespace :old_db do
  desc "Migrate old database"
  task old_db_migrate_companies: :environment do
    # This task will migrate the old database to the new schema
    # You can use ActiveRecord models and database queries to perform the migration

    require 'mysql2'

    begin
      # Create a new database connection.
      db = Mysql2::Client.new(
          :host => '************', # '************',
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      # Execute a SQL query.
      query = <<~SQL
        SELECT *
        FROM tbl_company
      SQL
      stmt = db.query(query)

      # tbl_company
      #{"CMPYID"=>"fee88f869754360e07f4bb600b152e44",
      # "ACTYPE"=>0,
      # "MEMBTYPE"=>nil,
      # "SUBMITDATE"=>2019-07-12 00:00:00 +0800,
      # "SUSPENDDATE"=>nil,
      # "STATUS"=>"1",
      # "CRTSYS"=>"2",
      # "TEL"=>"28519777",
      # "FAX"=>nil,
      # "EMAIL"=>"<EMAIL>",
      # "URL"=>nil,
      # "ISPUBLICSEARCHABLE"=>nil,
      # "IPGLN"=>"9588827030000",
      # "EXPIRYDATE"=>nil,
      # "AUTOGEN"=>"Y",
      # "COUNTRY"=>nil,
      # "INCHARGEPERSONTITLE"=>nil,
      # "INCHARGE_EMAIL"=>nil,
      # "INCHARGE_PHONE"=>nil,
      # "INCHARGE_FAX"=>nil,
      # "CONTACTTITLE"=>nil,
      # "CONTACT_EMAIL"=>"<EMAIL>",
      # "CONTACT_PHONE"=>"28519777",
      # "CONTACT_FAX"=>nil,
      # "REMARK"=>nil,
      # "LASTMDFUSER"=>"IMPORT",
      # "LASTMDFDATE"=>2019-07-12 00:00:00 +0800,
      # "VERSION"=>0,
      # "NONPUBIND"=>"1",
      # "CCSEXPIRY"=>nil,
      # "CRMNO"=>nil}

      stmt.each do |row|
          # Create a new record in the new database.
          c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

          c.ipgln = row['IPGLN']
          # c.prefix = row['PREFIX']
          # c.name = row['NAME']
          # c.name_cn = row['NAME_CN']
          # c.name_en = row['NAME_EN']
          c.email = row['EMAIL']
          c.website_url = row['URL']
          c.telephone = row['TEL']
          c.fax = row['FAX']
          # c.location = row['LOCATION']
          # c.location_cn = row['LOCATION_CN']
          # c.location_en = row['LOCATION_EN']
          # c.floor = row['FLOOR']
          # c.floor_cn = row['FLOOR_CN']
          # c.floor_en = row['FLOOR_EN']
          # c.street = row['STREET']
          # c.street_cn = row['STREET_CN']
          # c.street_en = row['STREET_EN']
          # c.city = row['CITY']
          # c.city_cn = row['CITY_CN']
          # c.city_en = row['CITY_EN']
          # c.country = row['COUNTRY']
          # c.in_charge_person_name = row['IN_CHARGE_PERSON_NAME']
          # c.in_charge_person_name_cn = row['IN_CHARGE_PERSON_NAME_CN']
          # c.in_charge_person_name_en = row['IN_CHARGE_PERSON_NAME_EN']
          # c.in_charge_job_title = row['IN_CHARGE_JOB_TITLE']
          c.in_charge_email = row['IN_CHARGE_EMAIL']
          c.in_charge_telephone = row['IN_CHARGE_PHONE']
          # c.contact_person_name = row['CONTACT_PERSON_NAME']
          # c.contact_person_name_cn = row['CONTACT_PERSON_NAME_CN']
          # c.contact_person_name_en = row['CONTACT_PERSON_NAME_EN']
          # c.contact_job_title = row['CONTACT_JOB_TITLE']
          c.contact_email = row['CONTACT_EMAIL']
          c.contact_telephone = row['CONTACT_PHONE']
          # c.admin_first_name = row['ADMIN_FIRST_NAME']
          # c.admin_first_name_cn = row['ADMIN_FIRST_NAME_CN']
          # c.admin_first_name_en = row['ADMIN_FIRST_NAME_EN']
          # c.admin_last_name = row['ADMIN_LAST_NAME']
          # c.admin_last_name_cn = row['ADMIN_LAST_NAME_CN']
          # c.admin_last_name_en = row['ADMIN_LAST_NAME_EN']
          # c.admin_title = row['ADMIN_TITLE']

          c.old_CMPYID = row['CMPYID']
          c.old_LASTMDFUSER = row['LASTMDFUSER']
          c.old_LASTMDFDATE = row['LASTMDFDATE']
          c.save
      end

      # tbl_company_ml
      #{"CMPYID"=>"4fc58c542fa62e4c0ca5018e600216b4",
      # "LANGID"=>"zh_CN",
      # "CMPYNAME_ML"=>"澳门润澳生物科技有限公司",
      # "ADDR1_ML"=>"南方大厦",
      # "ADDR2_ML"=>"4楼A座",
      # "ADDR3_ML"=>"友谊大马路1017号",
      # "CITY_ML"=>"澳门",
      # "INCHARGEPERSONNAME_ML"=>"石翰中",
      # "CONTACTNAME_ML"=>"石翰中",
      # "ID"=>1398}

      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_TW'
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        c.name = row['CMPYNAME_ML']
        c.location = row['ADDR1_ML']
        c.floor = row['ADDR2_ML']
        c.street = row['ADDR3_ML']
        c.city = row['CITY_ML']
        c.in_charge_person_name = row['INCHARGEPERSONNAME_ML']
        c.contact_person_name = row['CONTACTNAME_ML']
        c.save

      end

      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        c.name_cn = row['CMPYNAME_ML']
        c.location_cn = row['ADDR1_ML']
        c.floor_cn = row['ADDR2_ML']
        c.street_cn = row['ADDR3_ML']
        c.city_cn = row['CITY_ML']
        c.in_charge_person_name_cn = row['INCHARGEPERSONNAME_ML']
        c.contact_person_name_cn = row['CONTACTNAME_ML']
        c.save
      end

      query = <<~SQL
        SELECT *
        FROM tbl_company_ml
        WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        c = Company.find_or_initialize_by(old_CMPYID: row['CMPYID'])

        c.name_en = row['CMPYNAME_ML']
        c.location_en = row['ADDR1_ML']
        c.floor_en = row['ADDR2_ML']
        c.street_en = row['ADDR3_ML']
        c.city_en = row['CITY_ML']
        c.in_charge_person_name_en = row['INCHARGEPERSONNAME_ML']
        c.contact_person_name_en = row['CONTACTNAME_ML']
        c.save
      end

    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      exit 1
    ensure
      db.close if db
    end
  end

  task old_db_migrate_products: :environment do
    # This task will migrate the old database to the new schema
    # You can use ActiveRecord models and database queries to perform the migration

    require 'mysql2'

    begin
      # Create a new database connection.
      db = Mysql2::Client.new(
          :host => '************',
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      query = <<~SQL
        SELECT *
        FROM tbl_pd
        LIMIT 1
      SQL
      stmt = db.query(query)

      # {"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",
      # "GTIN"=>"9588853950167",
      # "IPGLN"=>"9588853950006",
      # "ISDRAFT"=>"N",
      # "ISBASE"=>"BASE",
      # "CLFNID"=>nil,
      # "PDGROSWGT"=>1371.0,
      # "PDPKGWGT"=>nil,
      # "PDWGTUOM"=>"GRM",
      # "PDNETCONT"=>1000.0,
      # "PDNETCONTUOM"=>"MLT",
      # "PCNTALCHBYVOL"=>nil,
      # "PDRCYC"=>nil,
      # "PDOUTRPKGRTRN"=>nil,
      # "PDOUTRPKGRCYC"=>nil,
      # "PDIGDT"=>nil,
      # "PDGNMD"=>nil,
      # "FRSHDATEPD"=>nil,
      # "PDBCD"=>nil,
      # "PDPRICED"=>nil,
      # "MTRLSAFESHEET"=>nil,
      # "DNGRGOODITEMNOLTR"=>nil,
      # "DNGRGOODSBSTID"=>nil,
      # "DNGRGOODTECHNAME"=>nil,
      # "PALTTYPECD"=>nil,
      # "PALTHNDL"=>nil,
      # "VARWGTTRDEITEM"=>nil,
      # "PDORDRUNITIND"=>nil,
      # "PDORDRQTYMIN"=>nil,
      # "PDORDRQTYMTPL"=>nil,
      # "RIGTOFRTN"=>nil,
      # "NONPUBIND"=>"1",
      # "INCLEXCLIND"=>nil,
      # "RLSEDT"=>nil,
      # "STRTAVILDT"=>nil,
      # "MFGRGLN"=>nil,
      # "MFGRNAME"=>nil,
      # "PDCNTYOFORGN"=>"MO",
      # "PDGRPID"=>nil,
      # "SUPYNO"=>nil,
      # "HMZDSYSCD"=>nil,
      # "NABCAPDCD"=>nil,
      # "PDDEEP"=>nil,
      # "PDHIGH"=>nil,
      # "PDWIDE"=>nil,
      # "PDDMTR"=>nil,
      # "PDSIZEUOM"=>nil,
      # "PDOUTRPKGTYPE"=>nil,
      # "NOOFCMPTLAYR"=>nil,
      # "NOOFTRDEITEMINLAYR"=>nil,
      # "NOOFLAYRPERPALT"=>nil,
      # "NOOFTRDEITEMPERPALTLAYR"=>nil,
      # "NOOFTRDEITEMPERPALT"=>nil,
      # "HAZDCD"=>nil,
      # "HAZDTYPECLFNSYST"=>nil,
      # "PDHNDLINFO"=>nil,
      # "STRGHNDLTEMPMAX"=>nil,
      # "STRGHNDLTEMPMIN"=>nil,
      # "STRGHNDLTEMPUOM"=>nil,
      # "MINLIFETIME"=>nil,
      # "LEADTIME"=>nil,
      # "LASTMDFUSER"=>"9588853950006_admin",
      # "LASTMDFDATE"=>2022-04-04 23:15:41 +0800,
      # "VERSION"=>0,
      # "ISBRANDOWNER"=>nil,
      # "ISMFGR"=>nil,
      # "ISDISTRIBUTOR"=>nil,
      # "ISRETAILER"=>nil,
      # "DNGRGOODTYPE"=>nil,
      # "PDVLMPU"=>nil,
      # "PDVLMUOM"=>nil,
      # "MFGRADDR"=>nil,
      # "MFGRTEL"=>nil,
      # "MFGRFAX"=>nil,
      # "MFGRMAIL"=>nil,
      # "MFGRSITE"=>nil,
      # "DSTBNAME"=>nil,
      # "DSTBADDR"=>nil,
      # "DSTBTEL"=>nil,
      # "DSTBFAX"=>nil,
      # "DSTBMAIL"=>nil,
      # "DSTBSITE"=>nil,
      # "CREATEDATE"=>2022-04-04 23:15:41 +0800,
      # "NONBIZIND"=>"1",
      # "OLD_CLFNCD"=>nil,
      # "GTIN14"=>"09588853950167",
      # "CLFNCDAGCY"=>nil,
      # "TGTMKTCNTYCD"=>nil,
      # "ENDAVILDT"=>nil,
      # "EFFCHGDT"=>nil,
      # "PDGRPIDMTNCAGCY"=>nil,
      # "PDPKGWGTUOM"=>nil,
      # "PDDNWGT"=>nil,
      # "PDDNWGTUOM"=>nil,
      # "PDFATCONT"=>nil,
      # "PDFATCONTUOM"=>nil,
      # "PDFATCONTBOM"=>nil,
      # "PDFATCONTBOMUOM"=>nil,
      # "PEGHOZT"=>nil,
      # "PEGHOZTUOM"=>nil,
      # "PEGVERT"=>nil,
      # "PEGVERTUOM"=>nil,
      # "PDOUTRPKGMTRLTYPE"=>nil,
      # "PDPKGMTRLCD"=>nil,
      # "PDPKGMTRLCDLSTMTNCAGCY"=>nil,
      # "STCKWGTMAXADMS"=>nil,
      # "STCKWGTUOM"=>nil,
      # "STCKFCTR"=>nil,
      # "BASEGTIN"=>nil,
      # "PDCOLRCDVALUE"=>nil,
      # "PDCOLRCDLISTMTNCAGCY"=>nil,
      # "PDSIZECDVALUE"=>nil,
      # "PDSIZECDLISTMTNCAGCY"=>nil,
      # "PDFORM"=>nil,
      # "PDSTNH"=>nil,
      # "PDSTNHUOM"=>nil,
      # "PDSTNHBOM"=>nil,
      # "PDSTNHBOMUOM"=>nil,
      # "PDINVUNITIND"=>nil,
      # "PDDSPHUNITIND"=>nil,
      # "BTCHNO"=>nil,
      # "DEGOFORGWORT"=>nil,
      # "BASEPRICECONTVALUE"=>nil,
      # "BASEPRICECONTVALUEUOM"=>nil,
      # "FRSTSALEDATE"=>nil,
      # "LEVYKIND"=>nil,
      # "LEVYCLAS"=>nil,
      # "LEVYAMT"=>nil,
      # "PKGMTRLCMPSOFPD"=>nil,
      # "ORDRSIZEFCTR"=>nil,
      # "CUPNFMLYCD"=>nil,
      # "CUPNNO"=>nil,
      # "LTRYGAMENO"=>nil,
      # "LTRYPACKBOOKNO"=>nil,
      # "FLSHPTTEMP"=>nil,
      # "TSDMARK"=>nil,
      # "QMARK"=>nil,
      # "TOPBAND"=>nil,
      # "GPC_CODE"=>"10000044",
      # "RETAILCLASS"=>nil,
      # "STATUS"=>"1",
      # "TRACEABILITY"=>nil}

      stmt.each do |row|
        # Create a new record in the new database.
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        p.company_id = Company.find_by(ipgln: row['IPGLN']).id
        p.gtin = row['GTIN']
        p.save
      end

      # tbl_pd_ml
      #{"PDID"=>"f6aa91ffecf655e753d9017f5fed0015",
      # "LANGID"=>"zh_TW",
      # "PDNAME_ML"=>"獅域·卡路 玫瑰味糖漿",
      # "PDBRNDNAME_ML"=>"獅域·卡路",
      # "PDDESC_ML"=>"用於調製各類飲品",
      # "PDPLUDESC_ML"=>nil,
      # "PDGRPIDDESC_ML"=>nil,
      # "ADDATTRDESC_ML"=>nil,
      # "PDCOLRDESC_ML"=>nil,
      # "PDSIZEDESC_ML"=>nil,
      # "PDBRNDOWNER_ML"=>nil,
      # "PDPDINGDT_ML"=>nil,
      # "PDGROSWGTDESC_ML"=>nil,
      # "PDUSEFUNC_ML"=>nil,
      # "PDDRTUSG_ML"=>nil,
      # "PDCAUTION_ML"=>nil,
      # "PDSTRGDESC_ML"=>nil,
      # "PDBENEFIT_ML"=>nil,
      # "PDREMARK_ML"=>nil,
      # "PDNETCONTDESC_ML"=>nil,
      # "PDPKGMTRLDESC"=>nil,
      # "PDCATDESC_ML"=>nil}
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        p.name = row['PDNAME_ML']
        p.brand_name = row['PDBRNDNAME_ML']
        p.description = row['PDDESC_ML']
        p.brand_owner_name = row['PDBRNDOWNER_ML']
        p.caution = row['PDCAUTION_ML']
        p.remark = row['PDREMARK_ML']
        p.save
      end
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'zh_CN'
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        p.name_cn = row['PDNAME_ML']
        p.brand_name_cn = row['PDBRNDNAME_ML']
        p.description_cn = row['PDDESC_ML']
        p.brand_owner_name_cn = row['PDBRNDOWNER_ML']
        p.caution_cn = row['PDCAUTION_ML']
        p.remark_cn = row['PDREMARK_ML']
        p.save
      end
      query = <<~SQL
        SELECT * FROM tbl_pd_ml WHERE LANGID = 'en'
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # Create a new record in the new database.
        p = Product.find_or_initialize_by(old_PDID: row['PDID'])

        p.name_en = row['PDNAME_ML']
        p.brand_name_en = row['PDBRNDNAME_ML']
        p.description_en = row['PDDESC_ML']
        p.brand_owner_name_en = row['PDBRNDOWNER_ML']
        p.caution_en = row['PDCAUTION_ML']
        p.remark_en = row['PDREMARK_ML']
        p.save
      end

    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      exit 1
    ensure
      db.close if db
    end
  end

  task old_db_migrate_users: :environment do
    # This task will migrate the old database to the new schema
    # You can use ActiveRecord models and database queries to perform the migration

    require 'mysql2'

    begin
      # Create a new database connection.
      db = Mysql2::Client.new(
          :host => '************',
          :username => 'readonly',
          :password => 'gs1@cpttm2022',
          :database => 'barcodeplus'
      )

      query = <<~SQL
        SELECT * FROM tbl_user
      SQL
      stmt = db.query(query)

      # {"USERID"=>"fd59c525e3de19c5c344955aa16eed49",
      # "CMPYID"=>"f9e36bae8a0d60f2a28f5bc599e51ce7",
      # "USERNAME"=>"9588818280001_admin",
      # "GENDER"=>nil,
      # "JOBTITLE"=>nil,
      # "TITLE"=>nil,
      # "EMAIL"=>"<EMAIL>",
      # "MOBILE"=>nil,
      # "TELNUM"=>nil,
      # "BIRTHDAY"=>nil,
      # "ISRECMAIL"=>nil,
      # "ISADMIN"=>"1",
      # "USERTYPE"=>"1",
      # "PASSWORD"=>"de6ee4771003bc3ed3a40567c5bc85f0f80607c86197bdf359045e4c81347ddb",
      # "PASSWORDHINT"=>nil,
      # "ISFIRSTLOGON"=>"Y",
      # "USERSTATUS"=>1,
      # "ACTIVECODE"=>nil,
      # "SESSIONID"=>nil,
      # "LASTLOGINTIME"=>2020-08-14 10:05:53 +0800,
      # "STARTTIME"=>nil,
      # "ENDTIME"=>nil,
      # "IPADDR"=>nil,
      # "SESSIONSTATUS"=>nil,
      # "CREATEDATE"=>2019-07-12 00:00:00 +0800,
      # "REMARK"=>nil,
      # "LASTMDFUSER"=>"IMPROT",
      # "LASTMDFDATE"=>2024-01-23 18:37:58 +0800,
      # "VERSION"=>0,
      # "MARTIALSTAT"=>nil,
      # "NUMCHILD"=>nil,
      # "EDULEVEL"=>nil,
      # "OCCUPATION"=>nil,
      # "HOW2KNOW"=>nil,
      # "LOCKCOUNT"=>0,
      # "LOCKTIME"=>nil}

      stmt.each do |row|
        # Create a new record in the new database.
        u = User.find_or_initialize_by(old_USERID: row['USERID'])

        company = Company.find_by(old_CMPYID: row['CMPYID'])
        if  company.nil?
          puts "Company not found for user #{row['EMAIL']}"
        else
          u.company_id = company.id
          u.email = row['EMAIL']
          u.old_PASSWORD = row['PASSWORD']
          u.old_PASSWORDHINT = row['PASSWORDHINT']
          u.old_USERNAME = row['USERNAME']
          u.password = "adfadfadfSDFDFDFsdf3423pgisdfu!#gSDFdfugifsd%gusp234dfgusdofg"
          u.save
        end
      end

      # {"USERID"=>"0",
      # "LANGID"=>"zh_TW",
      # "FIRSTNAME_ML"=>"System",
      # "LASTNAME_ML"=>"Admin",
      # "ADDR1_ML"=>nil,
      # "ADDR2_ML"=>nil,
      # "ADDR3_ML"=>nil,
      # "CITY_ML"=>nil,
      # "COUNTRY_ML"=>nil}
      query = <<~SQL
        SELECT * FROM tbl_user_ml WHERE LANGID = 'zh_TW'
        LIMIT 1
      SQL
      stmt = db.query(query)
      stmt.each do |row|
        # puts row
      end

    rescue Mysql2::Error => e
      puts "ERROR #{e.errno} (#{e.sqlstate}): #{e.error}"
      puts "Can't connect to the MySQL database specified."
      exit 1
    ensure
      db.close if db
    end
  end
end